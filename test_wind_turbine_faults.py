#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
风机故障诊断功能测试脚本

测试新增的风机故障类型和特征频率计算功能
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wind_turbine_fault_frequencies():
    """测试风机故障特征频率计算"""
    print("=" * 50)
    print("测试风机故障特征频率计算功能")
    print("=" * 50)
    
    # 模拟DeepSeekGUI类的风机频率计算方法
    def calculate_wind_turbine_fault_frequencies(rpm, blade_count=3, gear_ratio=1.0, pole_pairs=2):
        """
        计算风机故障特征频率
        
        Args:
            rpm (float): 转子转速 (RPM)
            blade_count (int): 叶片数量，默认3
            gear_ratio (float): 齿轮箱传动比，默认1.0（直驱）
            pole_pairs (int): 发电机极对数，默认2
            
        Returns:
            dict: 风机故障特征频率字典
        """
        # 转子转频 (Hz)
        rotor_freq = rpm / 60.0
        
        # 发电机转频 (Hz)
        generator_freq = rotor_freq * gear_ratio
        
        # 叶片通过频率 BPF (Blade Passing Frequency)
        bpf = rotor_freq * blade_count
        
        # 转子不平衡频率 (通常为1X转频及其谐波)
        unbalance_1x = rotor_freq
        unbalance_2x = rotor_freq * 2
        
        # 转子不对中频率 (通常为2X转频及其谐波)
        misalignment_2x = rotor_freq * 2
        misalignment_4x = rotor_freq * 4
        
        # 叶片故障频率 (叶片通过频率的谐波和边带)
        blade_fault_1x = bpf
        blade_fault_2x = bpf * 2
        
        # 齿轮箱故障频率 (如果有齿轮箱)
        if gear_ratio > 1.0:
            gear_mesh_freq = generator_freq * 20  # 假设齿轮齿数为20，实际应根据具体参数计算
        else:
            gear_mesh_freq = 0  # 直驱风机无齿轮箱
            
        # 发电机故障频率
        electrical_freq = generator_freq * pole_pairs  # 电气频率
        
        return {
            '转子转频': f"{rotor_freq:.2f}Hz",
            '叶片通过': f"{bpf:.2f}Hz",
            '转子不平衡1X': f"{unbalance_1x:.2f}Hz",
            '转子不平衡2X': f"{unbalance_2x:.2f}Hz", 
            '转子不对中2X': f"{misalignment_2x:.2f}Hz",
            '转子不对中4X': f"{misalignment_4x:.2f}Hz",
            '叶片故障1X': f"{blade_fault_1x:.2f}Hz",
            '叶片故障2X': f"{blade_fault_2x:.2f}Hz",
            '齿轮啮合': f"{gear_mesh_freq:.2f}Hz" if gear_ratio > 1.0 else "N/A",
            '电气频率': f"{electrical_freq:.2f}Hz"
        }
    
    # 测试不同类型的风机
    test_cases = [
        {
            'name': '直驱风机 (1800 RPM)',
            'rpm': 1800,
            'blade_count': 3,
            'gear_ratio': 1.0,
            'pole_pairs': 2
        },
        {
            'name': '齿轮箱风机 (30 RPM)',
            'rpm': 30,
            'blade_count': 3,
            'gear_ratio': 60.0,
            'pole_pairs': 2
        },
        {
            'name': '小型风机 (600 RPM)',
            'rpm': 600,
            'blade_count': 3,
            'gear_ratio': 3.0,
            'pole_pairs': 4
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['name']}:")
        print("-" * 30)
        fault_freqs = calculate_wind_turbine_fault_frequencies(
            case['rpm'], case['blade_count'], case['gear_ratio'], case['pole_pairs']
        )
        
        for fault_type, freq_value in fault_freqs.items():
            print(f"{fault_type:15}: {freq_value}")

def test_fault_type_mappings():
    """测试故障类型映射"""
    print("\n" + "=" * 50)
    print("测试故障类型中文映射")
    print("=" * 50)
    
    # 故障类型中文映射
    fault_type_chinese = {
        # 轴承故障类型
        'normal': '正常状态',
        'ball_fault': '滚动体故障',
        'inner_race_fault': '内圈故障',
        'outer_race_fault': '外圈故障',
        'IR': '内圈故障',
        'OR': '外圈故障',
        'B': '滚动体故障',
        'N': '正常状态',
        
        # 风机故障类型
        'rotor_unbalance': '转子不平衡',
        'rotor_misalignment': '转子不对中',
        'blade_fault': '叶片故障',
        'gearbox_fault': '齿轮箱故障',
        'generator_fault': '发电机故障',
        'RU': '转子不平衡',
        'RM': '转子不对中', 
        'BF': '叶片故障',
        'GF': '齿轮箱故障',
        'GEN': '发电机故障'
    }
    
    print("轴承故障类型:")
    bearing_faults = ['normal', 'ball_fault', 'inner_race_fault', 'outer_race_fault', 'IR', 'OR', 'B', 'N']
    for fault in bearing_faults:
        print(f"  {fault:20} -> {fault_type_chinese[fault]}")
    
    print("\n风机故障类型:")
    wind_faults = ['rotor_unbalance', 'rotor_misalignment', 'blade_fault', 'gearbox_fault', 'generator_fault', 'RU', 'RM', 'BF', 'GF', 'GEN']
    for fault in wind_faults:
        print(f"  {fault:20} -> {fault_type_chinese[fault]}")

def test_knowledge_base():
    """测试知识库导入"""
    print("\n" + "=" * 50)
    print("测试知识库导入")
    print("=" * 50)
    
    try:
        from bearing_knowledge_base import get_bearing_knowledge_base, get_wind_turbine_knowledge_base
        
        # 测试轴承知识库
        bearing_kb = get_bearing_knowledge_base()
        if bearing_kb:
            print("✅ 轴承知识库导入成功")
            print(f"   知识库长度: {len(bearing_kb)} 字符")
        else:
            print("❌ 轴承知识库为空")
        
        # 测试风机知识库
        wind_kb = get_wind_turbine_knowledge_base()
        if wind_kb:
            print("✅ 风机知识库导入成功")
            print(f"   知识库长度: {len(wind_kb)} 字符")
        else:
            print("❌ 风机知识库为空")
            
    except ImportError as e:
        print(f"❌ 知识库导入失败: {e}")

def generate_test_signal():
    """生成包含风机故障特征的测试信号"""
    print("\n" + "=" * 50)
    print("生成风机故障测试信号")
    print("=" * 50)
    
    # 信号参数
    fs = 1000  # 采样频率 Hz
    t = np.linspace(0, 10, fs * 10)  # 10秒信号
    
    # 风机参数
    rpm = 1800
    rotor_freq = rpm / 60.0  # 30 Hz
    blade_count = 3
    bpf = rotor_freq * blade_count  # 90 Hz
    
    # 生成包含多种故障特征的信号
    signal = np.zeros_like(t)
    
    # 正常运行基频
    signal += 0.5 * np.sin(2 * np.pi * rotor_freq * t)
    
    # 转子不平衡 (1X)
    signal += 0.3 * np.sin(2 * np.pi * rotor_freq * t + np.pi/4)
    
    # 转子不对中 (2X)
    signal += 0.2 * np.sin(2 * np.pi * 2 * rotor_freq * t)
    
    # 叶片通过频率
    signal += 0.15 * np.sin(2 * np.pi * bpf * t)
    
    # 添加噪声
    signal += 0.1 * np.random.normal(0, 1, len(t))
    
    # 计算频谱
    fft_signal = np.fft.fft(signal)
    freqs = np.fft.fftfreq(len(signal), 1/fs)
    
    # 只取正频率部分
    positive_freqs = freqs[:len(freqs)//2]
    positive_fft = np.abs(fft_signal[:len(fft_signal)//2])
    
    print(f"生成信号参数:")
    print(f"  采样频率: {fs} Hz")
    print(f"  信号长度: {len(t)} 点 ({len(t)/fs} 秒)")
    print(f"  转子转频: {rotor_freq:.2f} Hz")
    print(f"  叶片通过频率: {bpf:.2f} Hz")
    
    # 找到主要频率成分
    peak_indices = np.argsort(positive_fft)[-10:]  # 前10个最大峰值
    print(f"\n主要频率成分:")
    for idx in reversed(peak_indices):
        freq = positive_freqs[idx]
        amplitude = positive_fft[idx]
        if freq > 0 and amplitude > 0.1:  # 过滤小幅值
            print(f"  {freq:.2f} Hz: {amplitude:.3f}")
    
    return t, signal, positive_freqs, positive_fft

if __name__ == "__main__":
    print("风机故障诊断功能测试")
    print("=" * 60)
    
    # 运行所有测试
    test_wind_turbine_fault_frequencies()
    test_fault_type_mappings()
    test_knowledge_base()
    generate_test_signal()
    
    print("\n" + "=" * 60)
    print("测试完成！")
