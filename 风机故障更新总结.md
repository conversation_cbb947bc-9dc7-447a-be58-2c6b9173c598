# 风机故障诊断系统更新总结

## 更新概述

根据用户要求，已成功从风机故障诊断系统中去除了齿轮箱故障和发电机故障，现在系统专注于三种核心风机故障类型的诊断：

1. **转子不平衡** (Rotor Unbalance)
2. **转子不对中** (Rotor Misalignment)  
3. **叶片故障** (Blade Fault)

## 修改的文件

### 1. ds-api-debug_fin.py (主程序文件)

#### 修改内容：
- **风机故障频率计算函数** (第1672-1710行)
  - 移除了 `gear_ratio` 和 `pole_pairs` 参数
  - 删除了齿轮啮合频率和电气频率的计算
  - 简化了返回的频率字典

- **风机参数设置对话框** (第4550-4558行)
  - 移除了齿轮箱传动比和发电机极对数的输入控件
  - 简化了参数计算逻辑

- **故障诊断指导信息** (第4602-4606行)
  - 移除了齿轮箱故障和发电机故障的诊断指导

- **故障类型映射** (三个位置)
  - 移除了 'gearbox_fault', 'generator_fault', 'GF', 'GEN' 映射
  - 保留了 'rotor_unbalance', 'rotor_misalignment', 'blade_fault', 'RU', 'RM', 'BF'

### 2. dl_models.py (深度学习模型文件)

#### 修改内容：
- **故障类型映射** (第419-425行)
  - 更新了中文故障类型映射，移除齿轮箱和发电机故障类型

### 3. bearing_knowledge_base.py (知识库文件)

#### 修改内容：
- **风机故障类型章节** (第540-550行)
  - 移除了齿轮箱故障和发电机故障的详细描述
  - 简化了测点布置和频率分析重点

- **维护指南** (第578-591行)
  - 移除了齿轮箱和发电机相关的维护内容
  - 专注于三种核心故障的维护建议

### 4. test_wind_turbine_faults.py (测试脚本)

#### 修改内容：
- **频率计算函数** (第24-62行)
  - 移除了齿轮箱和发电机相关的参数和计算
  - 简化了测试用例

- **故障类型测试** (第111-128行)
  - 更新了故障类型映射测试
  - 移除了齿轮箱和发电机故障的测试

## 功能验证

### 1. 测试结果
运行 `test_wind_turbine_faults.py` 验证了：
- ✅ 风机故障特征频率计算正确
- ✅ 故障类型中文映射正确
- ✅ 知识库导入成功
- ✅ 故障信号生成正常

### 2. 演示脚本
创建了 `wind_turbine_demo.py` 演示脚本，展示了：
- 🌪️ 三种支持的风机故障类型
- 📊 特征频率计算示例
- 🔍 故障诊断原理
- 🔬 故障信号特征分析
- 🔧 维护建议指南

## 技术细节

### 故障特征频率

#### 转子不平衡
- **特征频率**: 1X转频 (30.00 Hz), 2X转频 (60.00 Hz)
- **故障机理**: 叶片积灰、叶片损伤、制造误差
- **振动特征**: 径向振动为主，1X转频幅值突出

#### 转子不对中
- **特征频率**: 2X转频 (60.00 Hz), 4X转频 (120.00 Hz)
- **故障机理**: 基础沉降、热膨胀、安装误差
- **振动特征**: 轴向振动明显，2X转频幅值突出

#### 叶片故障
- **特征频率**: 叶片通过频率 (90.00 Hz), 2X叶片通过频率 (180.00 Hz)
- **故障机理**: 叶片裂纹、积冰、疲劳损伤
- **振动特征**: BPF及其谐波幅值增大

### 计算公式

```python
# 基本频率计算
rotor_freq = rpm / 60.0  # 转子转频 (Hz)
bpf = rotor_freq * blade_count  # 叶片通过频率 (Hz)

# 故障特征频率
unbalance_1x = rotor_freq      # 转子不平衡1X
unbalance_2x = rotor_freq * 2  # 转子不平衡2X
misalignment_2x = rotor_freq * 2  # 转子不对中2X
misalignment_4x = rotor_freq * 4  # 转子不对中4X
blade_fault_1x = bpf           # 叶片故障1X
blade_fault_2x = bpf * 2       # 叶片故障2X
```

## 系统优势

### 1. 简化的参数设置
- 只需输入转速(RPM)和叶片数量
- 自动计算所有相关故障特征频率
- 界面更加简洁易用

### 2. 专业的故障诊断
- 专注于三种核心风机故障类型
- 基于深度学习的智能诊断
- 结合传统频谱分析和AI技术

### 3. 完整的知识库支持
- 详细的故障机理说明
- 专业的维护建议
- 标准化的报告生成

## 使用指南

### 1. 启动系统
```bash
python ds-api-debug_fin.py
```

### 2. 设置风机参数
- 点击菜单 "分析" → "🌪️ 风机参数"
- 输入转速(RPM)和叶片数量
- 系统自动计算故障特征频率

### 3. 进行故障诊断
- 加载振动信号数据
- 使用深度学习模型进行预测
- 结合频谱分析进行验证
- 生成诊断报告

## 总结

本次更新成功简化了风机故障诊断系统，去除了齿轮箱故障和发电机故障，专注于三种核心故障类型。系统现在更加专业、简洁，能够为风机运维提供准确的故障诊断和维护建议。

所有修改已经过测试验证，系统运行正常，功能完整。
