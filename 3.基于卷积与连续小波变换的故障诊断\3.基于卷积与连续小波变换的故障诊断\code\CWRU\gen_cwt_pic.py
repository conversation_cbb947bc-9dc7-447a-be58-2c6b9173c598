import random
import tensorflow as tf
import pywt
import matplotlib.pyplot as plt
import numpy as np
import preprocessing

'''
github：https://github.com/boating-in-autumn-rain?tab=repositories
网址：www.slothai.cn
微信公众号：秋雨行舟
B站：秋雨行舟
抖音：秋雨行舟
咨询微信：slothalone
'''

def gen_cwt_pic(length, save_pic_dir, data, label):
    for i in range(0, len(data)):
        N = length
        fs = 12000
        t = np.linspace(0, length / fs, N, endpoint=False)
        wavename = 'cmor3-3'
        totalscal = 256
        fc = pywt.central_frequency(wavename)
        cparam = 2 * fc * totalscal
        scales = cparam / np.arange(totalscal, 1, -1)

        [cwtmatr, frequencies] = pywt.cwt(data[i], scales, wavename, 1.0 / fs)
        plt.contourf(t, frequencies, abs(cwtmatr))

        plt.axis('off')
        plt.gcf().set_size_inches(length / 100, length / 100)
        plt.gca().xaxis.set_major_locator(plt.NullLocator())
        plt.gca().yaxis.set_major_locator(plt.NullLocator())
        plt.subplots_adjust(top=1, bottom=0, right=1, left=0, hspace=0, wspace=0)
        plt.margins(0, 0)
        x = r'%s' % save_pic_dir + str(i) + '-' + str(label[i]) + '.jpg'
        plt.savefig(x)


if __name__ == '__main__':
    path = r'../../data/CWRU/0HP'  # 数据集路径
    normal = True  # 是否需要标准化
    stride = 150  # 滑动步长
    length = 784  # 样本长度
    number = 60  # 生成样本数
    rate = [0.7, 0.15, 0.15]  # 划分比例

    # 1.数据集预处理
    x_train, y_train, x_valid, y_valid, x_test, y_test = preprocessing.prepro(d_path=path, length=length, number=number,
                                                                              normal=normal, rate=rate, stride=stride)

    # 调用生成图片函数
    save_pic_dir = r'../../cwt_picture/CWRU/test/'
    gen_cwt_pic(length, save_pic_dir, x_test, y_test)





