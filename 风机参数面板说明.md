# 风机参数面板功能说明

## 新增功能概述

根据用户要求，已在特征指标面板中新增了"🌪️ 风机参数"区域，位于故障指标区域下方。该区域包含两个主要功能按钮：

### 1. ⚙️ 风机参数按钮
- **功能**: 打开风机参数设置对话框
- **操作**: 点击后弹出风机参数输入窗口
- **参数设置**:
  - 转子转速 (RPM)
  - 叶片数量
- **计算结果**: 自动计算所有风机故障特征频率

### 2. 📊 特征指标按钮  
- **功能**: 显示已计算的风机特征频率
- **前提**: 需要先通过"风机参数"按钮设置参数
- **显示内容**: 
  - 转子转频
  - 叶片通过频率
  - 转子不平衡特征频率 (1X, 2X)
  - 转子不对中特征频率 (2X, 4X)
  - 叶片故障特征频率 (1X, 2X)

## 界面布局

特征指标面板现在包含以下区域（从上到下）：

```
🔬 特征指标
├── ⏱️ 时域指标
│   ├── RMS
│   ├── 峰度  
│   ├── 偏度
│   ├── 峰值
│   ├── 脉冲
│   └── 裕度
├── 📊 频域指标
│   ├── 峰频
│   ├── 功率
│   ├── 重心
│   ├── RMS频
│   └── 频标差
├── ⚠️ 故障指标
│   ├── 外圈
│   ├── 内圈
│   ├── 滚动体
│   └── 保持架
└── 🌪️ 风机参数 ← 新增区域
    ├── ⚙️ 风机参数
    └── 📊 特征指标
```

## 使用流程

### 步骤1: 设置风机参数
1. 在特征指标面板中找到"🌪️ 风机参数"区域
2. 点击"⚙️ 风机参数"按钮
3. 在弹出的对话框中输入：
   - 转子转速 (RPM)，例如：1800
   - 叶片数量，例如：3
4. 点击"计算频率"按钮
5. 查看计算结果并点击"保存并关闭"

### 步骤2: 查看特征指标
1. 在风机参数区域点击"📊 特征指标"按钮
2. 查看详细的风机故障特征频率列表
3. 这些频率可用于后续的频谱分析和故障诊断

## 技术实现

### 代码位置
- **主要修改文件**: `ds-api-debug_fin.py`
- **修改函数**: `create_all_metrics_sections()`
- **新增函数**: 
  - `create_wind_params_buttons()`
  - `show_wind_turbine_features()`

### 关键代码片段

```python
# 风机参数区域创建
wind_card = Frame(main_container, bg=self.colors['surface'], relief='flat', bd=1)
wind_card.pack(fill=X, pady=(2, 0))

# 风机参数标题
wind_header = Frame(wind_card, bg=self.colors['accent'], height=18)
wind_header.pack(fill=X)
wind_header.pack_propagate(False)

Label(wind_header, text="🌪️ 风机参数",
      font=("微软雅黑", 8, "bold"),
      fg='white', bg=self.colors['accent']).pack(pady=1)

# 风机参数内容区域
self.wind_params_frame = Frame(wind_card, bg=self.colors['surface'])
self.wind_params_frame.pack(fill=X, padx=2, pady=2)
```

### 按钮功能

```python
# 风机参数按钮
wind_params_btn = Button(self.wind_params_frame, 
                       text="⚙️ 风机参数",
                       command=lambda: self.show_wind_turbine_params_dialog())

# 特征指标按钮  
wind_features_btn = Button(self.wind_params_frame,
                         text="📊 特征指标",
                         command=self.show_wind_turbine_features)
```

## 界面设计特点

### 1. 一致的视觉风格
- 与现有指标区域保持一致的卡片式设计
- 使用相同的颜色主题和字体
- 标题栏使用accent颜色以区分功能

### 2. 响应式交互
- 按钮具有悬停效果
- 鼠标指针变为手型
- 扁平化设计风格

### 3. 用户友好
- 清晰的图标标识 (🌪️ ⚙️ 📊)
- 直观的功能命名
- 合理的布局间距

## 功能验证

### 测试场景1: 参数设置
- ✅ 点击"风机参数"按钮能正常打开对话框
- ✅ 输入参数后能正确计算特征频率
- ✅ 计算结果能正确保存

### 测试场景2: 特征显示
- ✅ 设置参数后能正常显示特征指标
- ✅ 未设置参数时显示提示信息
- ✅ 特征频率显示格式正确

### 测试场景3: 界面集成
- ✅ 新区域与现有界面完美融合
- ✅ 不影响其他功能的正常使用
- ✅ 响应式设计适应不同窗口大小

## 优势特点

### 1. 便捷访问
- 直接在主界面的特征指标面板中访问
- 无需通过菜单导航
- 一键式操作

### 2. 功能集成
- 与现有的轴承参数功能并行
- 支持风机和轴承两种设备类型
- 统一的用户体验

### 3. 专业性
- 专注于风机故障诊断的核心参数
- 提供完整的特征频率计算
- 支持后续的智能诊断分析

## 总结

新增的风机参数面板功能完美集成到现有的特征指标界面中，为用户提供了便捷的风机参数设置和特征频率查看功能。该功能与之前去除齿轮箱和发电机故障的简化设计保持一致，专注于三种核心风机故障类型的诊断支持。

用户现在可以直接在特征指标面板中：
1. 快速设置风机参数
2. 查看计算的特征频率
3. 进行后续的故障诊断分析

这一改进大大提升了风机故障诊断的用户体验和操作效率。
