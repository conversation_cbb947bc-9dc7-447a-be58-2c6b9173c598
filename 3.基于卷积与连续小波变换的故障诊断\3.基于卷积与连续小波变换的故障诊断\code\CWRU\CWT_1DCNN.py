import pandas as pd
from sklearn.metrics import confusion_matrix
import matplotlib.pyplot as plt
import random
import tensorflow.python.keras as keras
import tensorflow.python.keras.layers as layers
import numpy as np
import tensorflow as tf
from sklearn.manifold import TSNE
import preprocessing
from sklearn.metrics import classification_report
import warnings
from read_picture import read_directory
warnings .filterwarnings("ignore")
# 设置字体为SimHei，以支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

'''
github：https://github.com/boating-in-autumn-rain?tab=repositories
网址：www.slothai.cn
微信公众号：秋雨行舟
B站：秋雨行舟
抖音：秋雨行舟
咨询微信：slothalone
'''

# 保存最佳模型自定义类
class CustomModelCheckpoint(keras.callbacks.Callback):
    def __init__(self, model, path):
        self.model = model
        self.path = path
        self.best_loss = np.inf

    def on_epoch_end(self, epoch, logs=None):
        val_loss = logs['val_loss']
        if val_loss < self.best_loss:
            print("\nValidation loss decreased from {} to {}, saving save_model".format(self.best_loss, val_loss))
            self.model.save_weights(self.path, overwrite=True)
            self.best_loss = val_loss


# t-SNE初始可视化函数
def start_tsne(x_data, y_data, length, csv_filename, image_filename):
    # 输出提示信息
    print("正在进行初始输入数据的可视化...")

    # 调整输入数据的形状
    x_data_reshaped = tf.reshape(x_data, (len(x_data), length))

    # 执行 t-SNE 并保存转换后的数据
    X_tsne = TSNE(n_components=2, random_state=42).fit_transform(x_data_reshaped)

    # 创建一个DataFrame，保存t-SNE结果和标签
    tsne_df = pd.DataFrame(X_tsne, columns=["维度1", "维度2"])
    tsne_df['标签'] = y_data

    # 保存到 CSV 文件
    tsne_df.to_csv(csv_filename, index=False)
    print(f"t-SNE 结果已保存到 {csv_filename}")

    # 绘制 t-SNE 可视化
    plt.figure(figsize=(10, 10))

    # 使用离散的颜色映射，增强颜色区分
    cmap = plt.cm.get_cmap('tab20')

    # 定义散点图，使用更加离散的颜色
    scatter = plt.scatter(X_tsne[:, 0], X_tsne[:, 1], c=y_data, cmap=cmap, alpha=0.7, s=60, edgecolors='k')

    # 获取唯一类别
    unique_labels = np.unique(y_data)

    # 为类别添加图例
    for label in unique_labels:
        plt.scatter([], [], c=scatter.cmap(scatter.norm(label)), label=f"{label}", s=100, edgecolors='black')

    # 设置标题和标签（中文）
    plt.title("T-SNE 可视化", fontsize=16)
    plt.xlabel("维度 1", fontsize=16)
    plt.ylabel("维度 2", fontsize=16)

    # 美化图表
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.xticks(fontsize=10)
    plt.yticks(fontsize=10)

    # 添加图例（颜色点），图例标题为“标签”
    plt.legend(title="标签", fontsize=16, loc="best", markerscale=1, frameon=True)

    # 保存图片，设置dpi为600
    plt.savefig(image_filename, dpi=600, bbox_inches='tight')
    print(f"图片已保存到 {image_filename}")

    # 展示图像
    plt.show()


# t-sne结束可视化函数
def end_tsne(x_data, y_data, csv_filename, model_file, end_tsne_image_filename):
    print("训练结束的t-sne降维可视化")
    model.load_weights(filepath=model_file)

    hidden_features = model.predict(x_data)

    # 执行 t-SNE 并保存转换后的数据
    X_tsne = TSNE(n_components=2, random_state=42).fit_transform(hidden_features)

    # 创建一个DataFrame，保存t-SNE结果和标签
    tsne_df = pd.DataFrame(X_tsne, columns=["维度1", "维度2"])
    tsne_df['标签'] = y_data

    # 保存到 CSV 文件
    tsne_df.to_csv(csv_filename, index=False)
    print(f"t-SNE 结果已保存到 {csv_filename}")

    # 绘制 t-SNE 可视化
    plt.figure(figsize=(10, 10))

    # 使用离散的颜色映射，增强颜色区分
    cmap = plt.cm.get_cmap('tab20')

    # 定义散点图，使用更加离散的颜色
    scatter = plt.scatter(X_tsne[:, 0], X_tsne[:, 1], c=y_data, cmap=cmap, alpha=0.7, s=60, edgecolors='k')

    # 获取唯一类别
    unique_labels = np.unique(y_data)

    # 为类别添加图例
    for label in unique_labels:
        plt.scatter([], [], c=scatter.cmap(scatter.norm(label)), label=f"{label}", s=100, edgecolors='black')

    # 设置标题和标签（中文）
    plt.title("T-SNE 可视化", fontsize=16)
    plt.xlabel("维度 1", fontsize=16)
    plt.ylabel("维度 2", fontsize=16)

    # 美化图表
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.xticks(fontsize=10)
    plt.yticks(fontsize=10)

    # 添加图例（颜色点），图例标题为“标签”
    plt.legend(title="标签", loc="best", markerscale=1, frameon=True)

    # 保存图片，设置dpi为600
    plt.savefig(end_tsne_image_filename, dpi=600, bbox_inches='tight')
    print(f"图片已保存到 {end_tsne_image_filename}")

    # 展示图像
    plt.show()

# 绘制acc和loss曲线
def acc_loss_line(history, acc_loss_filename, acc_image_filename, loss_image_filename):
    print("绘制准确率和损失值曲线")
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']

    epochs = range(len(acc))

    # 保存数据到CSV文件
    data = {
        'epoch': list(epochs),
        'accuracy': acc,
        'val_accuracy': val_acc,
        'loss': loss,
        'val_loss': val_loss
    }
    df = pd.DataFrame(data)
    df.to_csv(acc_loss_filename, index=False)

    # 绘制accuracy曲线
    plt.plot(epochs, acc, 'r', linestyle='-.')
    plt.plot(epochs, val_acc, 'b', linestyle='dashdot')
    plt.title('训练集和验证集准确率曲线')
    plt.xlabel("训练轮次")
    plt.ylabel("准确率")
    plt.legend(["训练集准确率", "验证集准确率"])
    # 保存图片，设置dpi为600
    plt.savefig(acc_image_filename, dpi=600, bbox_inches='tight')
    print(f"图片已保存到 {acc_image_filename}")

    plt.figure()

    # 绘制loss曲线
    plt.plot(epochs, loss, 'r', linestyle='-.')
    plt.plot(epochs, val_loss, 'b', linestyle='dashdot')
    plt.title('训练集和验证集损失值曲线')
    plt.xlabel("训练轮次")
    plt.ylabel("损失值")
    plt.legend(["训练集损失值", "验证集损失值"])

    # 保存图片，设置dpi为600
    plt.savefig(loss_image_filename, dpi=600, bbox_inches='tight')
    print(f"图片已保存到 {loss_image_filename}")

    plt.show()


# 绘制混淆矩阵
def confusion(x_test, y_test, model, confusion_filename, confusion_image_filename):
    print("绘制混淆矩阵")

    # 预测结果
    y_predict = model.predict(x_test)
    y_pred_int = np.argmax(y_predict, axis=1)

    print("混淆矩阵输出结果：")
    print(classification_report(y_test, y_pred_int, digits=4))

    # 计算混淆矩阵
    con_mat = confusion_matrix(y_test.astype(str), y_pred_int.astype(str))
    print(con_mat)

    # 计算百分比
    con_mat_percent = con_mat.astype('float') / con_mat.sum(axis=1)[:, np.newaxis] * 100

    # 保存混淆矩阵到CSV文件，转为百分比格式
    con_mat_percent_df = pd.DataFrame(con_mat_percent,
                                      index=[f'真实_{cls}' for cls in np.unique(y_test)],
                                      columns=[f'预测_{cls}' for cls in np.unique(y_pred_int)])

    con_mat_percent_df.to_csv(confusion_filename, float_format='%.1f')
    print(f"混淆矩阵已保存到 {confusion_filename}")

    # 类别标签
    classes = list(set(y_test))
    classes.sort()

    plt.figure(figsize=(10, 7))
    plt.imshow(con_mat_percent, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('混淆矩阵', fontsize=16)
    plt.colorbar()
    plt.xticks(np.arange(len(classes)), classes, rotation=45)
    plt.yticks(np.arange(len(classes)), classes)

    # 添加百分比文本
    threshold = con_mat_percent.max() / 2.
    for i in range(len(classes)):
        for j in range(len(classes)):
            plt.text(j, i, f"{con_mat_percent[i, j]:.1f}%",
                     ha='center', va='center',
                     color='white' if con_mat_percent[i, j] > threshold else 'black')

    plt.ylabel('真实标签', fontsize=16)
    plt.xlabel('预测标签', fontsize=16)
    plt.tight_layout()

    # 保存图片，设置dpi为600
    plt.savefig(confusion_image_filename, dpi=600, bbox_inches='tight')
    print(f"图片已保存到 {confusion_image_filename}")

    plt.show()

# 对输入到模型中的数据进一步处理
def data_pre(train_filedir, valid_filedir, test_filedir, height, channel):
    height = height
    width = height
    # 小波时频图输入
    x_train, y_train = read_directory(train_filedir, height, width, normal=1)
    x_valid, y_valid = read_directory(valid_filedir, height, width, normal=1)
    x_test, y_test = read_directory(test_filedir, height, width, normal=1)

    x_train = np.squeeze(x_train)
    x_valid = np.squeeze(x_valid)
    x_test = np.squeeze(x_test)

    x_train = np.expand_dims(x_train, axis=channel)
    x_valid = np.expand_dims(x_valid, axis=channel)
    x_test = np.expand_dims(x_test, axis=channel)
    y_train = [int(i) for i in y_train]
    y_valid = [int(i) for i in y_valid]
    y_test = [int(i) for i in y_test]
    x_train = np.array(x_train)
    y_train = np.array(y_train)
    x_valid = np.array(x_valid)
    y_valid = np.array(y_valid)
    x_test = np.array(x_test)
    y_test = np.array(y_test)

    index = [i for i in range(len(y_train))]
    random.shuffle(index)
    x_train = np.array(x_train)[index]
    y_train = np.array(y_train)[index]

    index = [i for i in range(len(y_valid))]
    random.shuffle(index)
    x_valid = np.array(x_valid)[index]
    y_valid = np.array(y_valid)[index]

    index2 = [i for i in range(len(y_test))]
    random.shuffle(index2)
    x_test = np.array(x_test)[index2]
    y_test = np.array(y_test)[index2]

    x_train = tf.reshape(x_train, (len(x_train), height * width, channel))
    x_valid = tf.reshape(x_valid, (len(x_valid), height * width, channel))
    x_test = tf.reshape(x_test, (len(x_test), height * width, channel))

    print("x_train.shape: ", x_train.shape)
    print("y_train.shape: ", y_train.shape)
    print("x_valid.shape: ", x_valid.shape)
    print("y_valid.shape: ", y_valid.shape)
    print("x_test.shape: ", x_test.shape)
    print("y_test.shape: ", y_test.shape)

    return x_train, y_train, x_valid, y_valid, x_test, y_test

# 模型定义
def mymodel(x_train):
    inputs = keras.Input(shape=(x_train.shape[1], x_train.shape[2]))
    h1 = layers.Conv1D(filters=8, kernel_size=3, strides=1, padding='same', activation='relu')(inputs)
    h1 = layers.MaxPool1D(pool_size=2, strides=2, padding='same')(h1)

    h1 = layers.Conv1D(filters=16, kernel_size=3, strides=1, padding='same', activation='relu')(h1)
    h1 = layers.MaxPool1D(pool_size=2, strides=2, padding='same')(h1)

    h1 = layers.Conv1D(filters=8, kernel_size=4, strides=1, padding='same', activation='relu')(h1)

    h1 = layers.Dropout(0.6)(h1)
    h1 = layers.Flatten()(h1)
    h1 = layers.Dense(64, activation='relu')(h1)
    h1 = layers.Dense(10, activation='softmax')(h1)

    deep_model = keras.Model(inputs, h1, name="cnn")
    return deep_model

def model_train(x_train, y_train, x_valid, y_valid, x_test, y_test, save_model_filename, batch_size, epochs):
    model = mymodel(x_train)

    # 打印模型参数
    model.summary()

    # 编译模型
    model.compile(optimizer=keras.optimizers.Adam(), loss='sparse_categorical_crossentropy', metrics=['accuracy'])

    # 模型训练
    history = model.fit(x_train, y_train,
                        batch_size=batch_size, epochs=epochs, verbose=1,
                        validation_data=(x_valid, y_valid),
                        callbacks=[CustomModelCheckpoint(model, save_model_filename)])

    return history, model

def model_test(model, save_model_filename):
    # 加载模型
    model.load_weights(filepath=save_model_filename)

    # 编译模型
    model.compile(loss='sparse_categorical_crossentropy', optimizer=keras.optimizers.Adam(), metrics=['accuracy'])

    # 评估模型
    scores = model.evaluate(x_test, y_test, verbose=1)
    print("测试集结果： ", '%s: %.2f%%' % (model.metrics_names[1], scores[1] * 100))


# main函数
if __name__ == '__main__':
    start_tsne_csv_filename = "../../save_csv/CWRU/CWT_1DCNN/start_tsne_data.csv"
    end_tsne_csv_filename = "../../save_csv/CWRU/CWT_1DCNN/end_tsne_data.csv"
    save_model_filename = "../../save_model/CWRU/CWT_1DCNN.h5"
    batch_size = 128 #  训练批次
    epochs = 100  # 训练轮次
    acc_loss_filename = '../../save_csv/CWRU/CWT_1DCNN/training_history.csv'
    confusion_filename = '../../save_csv/CWRU/CWT_1DCNN/confusion.csv'

    start_tsne_image_filename = '../../save_picture/CWRU/CWT_1DCNN/start_tsne.png'
    end_tsne_image_filename = '../../save_picture/CWRU/CWT_1DCNN/end_tsne.png'
    acc_image_filename = '../../save_picture/CWRU/CWT_1DCNN/acc.png'
    loss_image_filename = '../../save_picture/CWRU/CWT_1DCNN/loss.png'
    confusion_image_filename = '../../save_picture/CWRU/CWT_1DCNN/confusion.png'

    train_filedir = r'../../cwt_picture/CWRU/train/'
    valid_filedir = r'../../cwt_picture/CWRU/valid/'
    test_filedir = r'../../cwt_picture/CWRU/test/'
    height = 52
    channel = 3

    # 1.数据集预处理
    x_train, y_train, x_valid, y_valid, x_test, y_test = data_pre(train_filedir, valid_filedir, test_filedir, height, channel)

    # 2.训练开始的t-sne降维可视化
    start_tsne(x_train, y_train, height*height*channel, start_tsne_csv_filename, start_tsne_image_filename)

    # 3.一维卷积神经网络模型训练
    history, model = model_train(x_train, y_train, x_valid, y_valid, x_test, y_test, save_model_filename, batch_size, epochs)

    # 4.调用保存模型，测试集进行测试
    model_test(model, save_model_filename)

    # 5.准确率与损失值曲线展示
    acc_loss_line(history, acc_loss_filename, acc_image_filename, loss_image_filename)

    # 6.训练结束的t-sne降维可视化
    end_tsne(x_train, y_train, end_tsne_csv_filename, save_model_filename, end_tsne_image_filename)

    # 7.混淆矩阵展示
    confusion(x_test, y_test, model, confusion_filename, confusion_image_filename)




