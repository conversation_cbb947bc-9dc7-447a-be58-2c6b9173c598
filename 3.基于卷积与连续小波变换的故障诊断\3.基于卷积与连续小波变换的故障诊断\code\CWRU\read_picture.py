import numpy as np
import os
from PIL import Image

'''
github：https://github.com/boating-in-autumn-rain?tab=repositories
网址：www.slothai.cn
微信公众号：秋雨行舟
B站：秋雨行舟
抖音：秋雨行舟
咨询微信：slothalone
'''


def read_directory(directory_name, height, width, normal):
    file_list = os.listdir(directory_name)
    file_list.sort(key=lambda x: int(x.split('-')[0]))
    img = []
    label0 = []

    for each_file in file_list:
        img0 = Image.open(directory_name + '/' + each_file)
        # 将图像转换为 RGB 模式，保留三个通道
        img0 = img0.convert('RGB')
        rgb_image = img0.resize((height, width))
        img.append(np.array(rgb_image).astype(float))
        # 提取文件名中的标签信息
        label0.append(float(each_file.split('.')[0][-1]))

    # 如果需要归一化，则将所有像素值除以 255.0
    if normal:
        data = np.array(img) / 255.0
    else:
        data = np.array(img)

    # 调整数据的形状以适应 RGB 图像，3 通道对应 RGB
    data = data.reshape(-1, height, width, 3)
    label = np.array(label0)

    return data, label
