# -*- coding: utf-8 -*-
"""
DeepSeek故障诊断助手 - 提供机械故障诊断和数据分析功能
新增连续小波变换功能：
    1. 丰富小波变换结果展示（显示连续小波变换时频图，支持交互式参数调节，可选择"morl"、"cmor"、"mexh"等小波基）
    2. 提供交互式小波参数调节（选择小波基和分解层数）
    3. 实现流式输出，调用DeepSeek API时实时显示生成结果
"""

# 标准库导入
import os
import sys
import time
import threading
import json
from datetime import datetime

# 第三方库导入 - 按字母顺序排列
import matplotlib
matplotlib.use("TkAgg")  # 使用TkAgg后端
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import numpy as np
import pandas as pd
import pywt
from openai import OpenAI
import PyPDF2
import pytesseract
import re
import scipy.io
import scipy.signal
from tkinter import *
from tkinter import ttk, filedialog, scrolledtext, messagebox
from docx import Document

# 重新导入PIL模块，确保不被tkinter覆盖
from PIL import Image, ImageTk, ImageOps, ImageDraw, ImageFont

# 数据采集相关库导入
try:
    from mcculw import ul
    from mcculw.enums import ULRange, DigitalIODirection
    import torch
    import ctypes
    DAQ_AVAILABLE = True
    print("✅ 数据采集库加载成功")
except ImportError as e:
    print(f"⚠️  数据采集库导入失败: {e}")
    print("   数据采集功能将被禁用，但其他功能正常可用")
    DAQ_AVAILABLE = False
    ul = None
    ULRange = None
    DigitalIODirection = None
    torch = None
except Exception as e:
    print(f"❌ 数据采集库加载失败: {e}")
    print("   这通常是因为缺少MCC DAQ驱动程序或相关依赖")
    DAQ_AVAILABLE = False
    ul = None
    ULRange = None
    DigitalIODirection = None
    torch = None

# 深度学习相关库导入 - 专注使用PyTorch和1D CNN
DL_AVAILABLE = False

# 导入PyTorch和相关库
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    from torch.utils.data import DataLoader, TensorDataset
    from sklearn.metrics import confusion_matrix, classification_report, accuracy_score
    from sklearn.manifold import TSNE
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import matplotlib.pyplot as plt
    DL_AVAILABLE = True
    print("✅ 深度学习库加载成功 (PyTorch + 1D CNN)")
except ImportError as e:
    print(f"❌ PyTorch导入失败: {e}")
    print("   请安装PyTorch: pip install torch torchvision")
    print("   深度学习功能将被禁用，但其他功能正常可用")
    DL_AVAILABLE = False
except Exception as e:
    print(f"❌ PyTorch加载失败: {e}")
    DL_AVAILABLE = False

# 本地模块导入
try:
    from bearing_knowledge_base import get_bearing_knowledge_base, get_wind_turbine_knowledge_base
except ImportError:
    # 如果无法导入知识库模块，创建默认函数
    def get_bearing_knowledge_base():
        return None
    def get_wind_turbine_knowledge_base():
        return None

# 专注使用1D CNN深度学习模型
# 移除简化后端，专注PyTorch实现


class DeepSeekGUI:
    """DeepSeek故障诊断助手主类"""

    def __init__(self, master):
        """初始化主应用程序

        Args:
            master: 主窗口对象
        """
        self.master = master

        # 设置工作目录为当前目录（与主程序同一目录）
        self.work_dir = os.path.dirname(os.path.abspath(__file__))

        # 设置所有文件路径到当前目录下
        self.settings_file = os.path.join(self.work_dir, "settings.json")

        # 科技感配色方案 - 深色主题
        self.colors = {
            'primary': '#0f172a',      # 深空蓝 - 主背景
            'secondary': '#1e293b',    # 石板灰 - 次要背景
            'accent': '#06b6d4',       # 青色 - 强调色
            'accent_bright': '#00d9ff', # 亮青色 - 高亮
            'surface': '#334155',      # 表面色
            'surface_light': '#475569', # 浅表面色
            'background': '#f8fafc',   # 背景色（兼容性）
            'text_primary': '#f1f5f9', # 浅色文字
            'text_secondary': '#94a3b8', # 次要文字
            'text_accent': '#38bdf8',  # 强调文字
            'border': '#475569',       # 边框色
            'border_light': '#64748b', # 浅边框
            'success': '#10b981',      # 成功色
            'warning': '#f59e0b',      # 警告色
            'error': '#ef4444',        # 错误色
            'glow': '#06b6d4',         # 发光效果色
            'gradient_start': '#0f172a', # 渐变起始
            'gradient_end': '#1e293b'    # 渐变结束
        }

        # 创建企业级背景图片
        self.create_background_image()

        # 显示启动进度条
        self.show_splash()

        # ------------------------------
        # DeepSeek API信息
        # ------------------------------
        self.load_settings()

        # ------------------------------
        # 全局状态 & 文件
        # ------------------------------
        self.attachments = []
        self.preview_window = None
        self.current_image = None
        self.preview_type = "image"
        self.is_processing = False
        self.stop_generation = False  # 停止生成标志

        # 设置日志文件路径到deepseek助手文件夹下
        self.log_file = os.path.join(self.work_dir, "conversation_log.txt")
        self.mat_feature_log_file = os.path.join(self.work_dir, "mat_features_log.txt")

        # 确保日志文件夹存在
        self.init_log_files()

        # 主题设置
        self.theme = StringVar(value="default")

        # ============== 多轮对话上下文列表 ==============
        self.messages = [
            {
                "role": "system",
                "content": "你是故障诊断领域专业的助手，请用正式且专业的语气回答用户的问题。"
            }
        ]
        # ===============================================

        # ============== 数据采集相关初始化 ==============
        self.init_data_acquisition()

        # ============== DIO控制初始化 ==============
        self.init_dio_control()

        # ============== 深度学习相关初始化 ==============
        self.init_deep_learning()

        # 初始化界面和功能
        self.configure_ocr()
        self.setup_ui()
        self.post_init_check()

        # 检查API连接
        self.check_api_connection()

        # 关闭启动进度条
        self.close_splash()

    def create_background_image(self):
        """创建轴承主题背景图片"""
        try:
            # 创建背景图片
            width, height = 1200, 820
            background = Image.new('RGB', (width, height), self.colors['background'])
            draw = ImageDraw.Draw(background)

            # 绘制轻微的渐变背景
            for y in range(height):
                ratio = y / height
                r = int(248 + (240 - 248) * ratio)  # 更浅的渐变
                g = int(250 + (245 - 250) * ratio)
                b = int(252 + (250 - 252) * ratio)
                color = f'#{r:02x}{g:02x}{b:02x}'
                draw.line([(0, y), (width, y)], fill=color)

            # 绘制多个轴承图案作为背景装饰
            self.draw_bearing_background_patterns(draw, width, height)

            # 保存背景图片
            bg_path = os.path.join(self.work_dir, "background.png")
            background.save(bg_path)
            self.background_image = ImageTk.PhotoImage(background)

        except Exception as e:
            print(f"创建背景图片失败: {e}")
            self.background_image = None

    def draw_bearing_background_patterns(self, draw, width, height):
        """绘制轴承主题背景图案"""
        try:
            # 使用非常浅的颜色，不影响前景内容
            pattern_color = '#f0f4f8'

            # 在背景绘制多个大小不同的轴承图案
            bearing_positions = [
                (width * 0.15, height * 0.2, 80),   # 左上，大轴承
                (width * 0.85, height * 0.15, 60),  # 右上，中轴承
                (width * 0.1, height * 0.7, 50),    # 左下，小轴承
                (width * 0.9, height * 0.8, 70),    # 右下，中大轴承
                (width * 0.5, height * 0.9, 40),    # 底部中央，小轴承
            ]

            for x, y, radius in bearing_positions:
                self.draw_detailed_bearing(draw, x, y, radius, pattern_color)

            # 绘制轻微的技术线条
            self.draw_technical_lines(draw, width, height, pattern_color)

        except Exception as e:
            print(f"绘制轴承背景图案失败: {e}")

    def draw_detailed_bearing(self, draw, center_x, center_y, radius, color):
        """绘制详细的轴承图案"""
        try:
            # 外圈
            draw.ellipse([center_x - radius, center_y - radius,
                         center_x + radius, center_y + radius],
                        outline=color, width=2)

            # 内圈
            inner_radius = radius * 0.5
            draw.ellipse([center_x - inner_radius, center_y - inner_radius,
                         center_x + inner_radius, center_y + inner_radius],
                        outline=color, width=2)

            # 滚动体（钢球）
            ball_radius = radius * 0.08
            ball_orbit_radius = radius * 0.75
            num_balls = max(6, int(radius / 10))  # 根据轴承大小调整钢球数量

            import math
            for i in range(num_balls):
                angle = i * 2 * math.pi / num_balls
                ball_x = center_x + ball_orbit_radius * math.cos(angle)
                ball_y = center_y + ball_orbit_radius * math.sin(angle)

                draw.ellipse([ball_x - ball_radius, ball_y - ball_radius,
                             ball_x + ball_radius, ball_y + ball_radius],
                            outline=color, width=1)

            # 中心轴
            center_radius = radius * 0.15
            draw.ellipse([center_x - center_radius, center_y - center_radius,
                         center_x + center_radius, center_y + center_radius],
                        outline=color, width=1)

        except Exception as e:
            print(f"绘制详细轴承失败: {e}")

    def draw_technical_lines(self, draw, width, height, color):
        """绘制技术线条装饰"""
        try:
            # 绘制一些技术感的线条
            line_color = color

            # 水平技术线
            for i in range(3):
                y = height * (0.3 + i * 0.2)
                draw.line([(width * 0.05, y), (width * 0.25, y)], fill=line_color, width=1)
                draw.line([(width * 0.75, y), (width * 0.95, y)], fill=line_color, width=1)

            # 垂直技术线
            for i in range(2):
                x = width * (0.3 + i * 0.4)
                draw.line([(x, height * 0.05), (x, height * 0.15)], fill=line_color, width=1)
                draw.line([(x, height * 0.85), (x, height * 0.95)], fill=line_color, width=1)

        except Exception as e:
            print(f"绘制技术线条失败: {e}")

    def init_data_acquisition(self):
        """初始化数据采集相关参数"""
        # 数据采集参数
        self.daq_params = {
            'board_num': 0,
            'channel': 0,
            'range': ULRange.BIP10VOLTS if DAQ_AVAILABLE and ULRange else None,
            'sample_rate': 12000,      # 采样频率 Hz
            'num_points': 1000,        # 采样点数
            'sample_time': 1.0,        # 采样时间 秒
            'device_id': "Sensor01",
            'bearing_model': "6205",
            'rpm': 1797,
            'ball_count': 9,
            'ball_diameter': 7.94,     # mm
            'pitch_diameter': 39.04,   # mm
            'contact_angle': 0         # 度
        }

        # 数据采集状态
        self.daq_running = False
        self.daq_thread = None
        self.daq_data_buffer = []

        # DIO控制参数
        self.dio_params = {
            'board_num': 0,
            'dio_port': 0,      # DIO端口号 (尝试不同端口: 0, 1, 2)
            'dio_bit': 1,       # DIO1对应bit 1
            'current_state': 0, # 当前输出状态
            'use_bit_operations': True,  # 是否使用位操作
            'fallback_ports': [0, 1, 2]  # 备用端口列表
        }

        # 深度学习预测相关
        self.dl_prediction_results = None
        self.fault_detection_enabled = True  # 是否启用故障检测DIO输出

        # DeepSeek API参数配置文件路径
        self.api_config_file = os.path.join(self.work_dir, "api_config.json")

        # DeepSeek API参数配置 - 从文件加载或使用默认值
        self.api_params = self.load_api_config()

        # 实时显示相关
        self.realtime_data_buffer = []  # 实时显示数据缓冲区
        self.max_display_points = 1000  # 最大显示点数
        self.realtime_fig = None
        self.realtime_ax = None
        self.realtime_canvas = None
        self.realtime_line = None

        # 创建data文件夹
        self.daq_save_path = os.path.join(self.work_dir, "data")
        if not os.path.exists(self.daq_save_path):
            os.makedirs(self.daq_save_path)

        # 数据采集UI变量 - 基本参数
        self.daq_sample_rate_var = StringVar(value=str(self.daq_params['sample_rate']))
        self.daq_num_points_var = StringVar(value=str(self.daq_params['num_points']))
        self.daq_device_id_var = StringVar(value=self.daq_params['device_id'])
        self.daq_status_var = StringVar(value="就绪")

        # DIO状态显示变量
        self.dio_status_var = StringVar(value="低电平")

        # 数据采集UI变量 - 硬件配置参数
        self.daq_board_num_var = StringVar(value=str(self.daq_params['board_num']))
        self.daq_channel_var = StringVar(value=str(self.daq_params['channel']))

        # 数据采集UI变量 - 轴承参数（用于Excel命名）
        self.daq_bearing_id_var = StringVar(value="Bearing01")
        self.daq_bearing_model_var = StringVar(value=self.daq_params['bearing_model'])
        self.daq_rpm_var = StringVar(value=str(self.daq_params['rpm']))
        self.daq_ball_count_var = StringVar(value=str(self.daq_params['ball_count']))
        self.daq_ball_diameter_var = StringVar(value=str(self.daq_params['ball_diameter']))
        self.daq_pitch_diameter_var = StringVar(value=str(self.daq_params['pitch_diameter']))
        self.daq_contact_angle_var = StringVar(value=str(self.daq_params['contact_angle']))

        # Excel文件命名预览
        self.daq_filename_preview_var = StringVar(value="")
        self.update_filename_preview()

    def init_dio_control(self):
        """初始化DIO数字输出控制"""
        try:
            if DAQ_AVAILABLE and ul:
                print("🔧 开始初始化DIO控制...")

                # 尝试自动检测可用的DIO端口
                board_num = self.dio_params['board_num']
                fallback_ports = self.dio_params['fallback_ports']

                dio_port_found = False

                for test_port in fallback_ports:
                    try:
                        print(f"   测试DIO端口 {test_port}...")

                        # 尝试配置端口为输出模式
                        if DigitalIODirection:
                            ul.d_config_port(board_num, test_port, DigitalIODirection.OUT)

                        # 尝试读取端口状态
                        current_value = ul.d_in(board_num, test_port)
                        print(f"   端口 {test_port} 当前值: {current_value}")

                        # 尝试写入测试值
                        ul.d_out(board_num, test_port, 0)
                        verify_value = ul.d_in(board_num, test_port)

                        print(f"✅ DIO端口 {test_port} 测试成功")
                        self.dio_params['dio_port'] = test_port
                        dio_port_found = True
                        break

                    except Exception as port_error:
                        print(f"   端口 {test_port} 测试失败: {port_error}")
                        continue

                if dio_port_found:
                    print(f"✅ DIO端口 {self.dio_params['dio_port']} 已配置为输出模式")

                    # 初始化DIO1为低电平（正常状态）
                    self.set_dio_output(0)
                    print("✅ DIO控制初始化完成")
                else:
                    print("❌ 未找到可用的DIO端口")
                    print("   请检查：")
                    print("   1. MCC数据采集卡是否正确连接")
                    print("   2. 驱动程序是否正确安装")
                    print("   3. 硬件是否支持数字IO功能")

            else:
                print("⚠️  DIO控制功能不可用：数据采集硬件未连接")
                print(f"   DAQ_AVAILABLE: {DAQ_AVAILABLE}")
                print(f"   ul模块: {ul is not None}")

        except Exception as e:
            print(f"❌ DIO控制初始化失败: {e}")
            import traceback
            traceback.print_exc()

    def set_dio_output(self, state):
        """设置DIO1输出状态

        Args:
            state (int): 输出状态，0=低电平，1=高电平
        """
        try:
            print(f"🔧 尝试设置DIO1输出状态: {state} ({'高电平' if state else '低电平'})")
            print(f"   DAQ_AVAILABLE: {DAQ_AVAILABLE}")
            print(f"   ul模块: {ul is not None}")

            if not DAQ_AVAILABLE or not ul:
                print("⚠️  DIO控制不可用：硬件未连接")
                print("   这可能是因为：")
                print("   1. MCC数据采集卡未连接")
                print("   2. MCC驱动程序未安装")
                print("   3. mcculw Python库未安装")
                print("   4. 硬件权限不足")

                # 即使硬件不可用，也更新界面显示（用于测试）
                state_text = "高电平" if state else "低电平"
                if hasattr(self, 'dio_status_var'):
                    self.dio_status_var.set(f"{state_text}(模拟)")
                if hasattr(self, 'dio_status_label'):
                    color = self.colors['error'] if state else self.colors['success']
                    self.dio_status_label.config(fg=color)

                return False

            board_num = self.dio_params['board_num']
            dio_port = self.dio_params['dio_port']
            dio_bit = self.dio_params['dio_bit']

            print(f"   板卡号: {board_num}")
            print(f"   DIO端口: {dio_port}")
            print(f"   DIO位: {dio_bit}")

            # 读取当前端口状态
            current_port_value = ul.d_in(board_num, dio_port)
            print(f"   当前端口值: {current_port_value:08b} ({current_port_value})")

            # 设置或清除指定位
            if state:
                # 设置DIO1为高电平
                new_port_value = current_port_value | (1 << dio_bit)
                print(f"   设置位{dio_bit}为1")
            else:
                # 设置DIO1为低电平
                new_port_value = current_port_value & ~(1 << dio_bit)
                print(f"   清除位{dio_bit}为0")

            print(f"   新端口值: {new_port_value:08b} ({new_port_value})")

            # 输出到端口
            ul.d_out(board_num, dio_port, new_port_value)
            print(f"   已写入端口")

            # 验证写入结果
            verify_value = ul.d_in(board_num, dio_port)
            print(f"   验证端口值: {verify_value:08b} ({verify_value})")

            # 更新状态记录
            self.dio_params['current_state'] = state

            state_text = "高电平" if state else "低电平"
            print(f"✅ DIO1输出已设置为{state_text}")

            # 更新界面显示（如果界面已创建）
            if hasattr(self, 'dio_status_var'):
                self.dio_status_var.set(state_text)
                print(f"   界面状态已更新: {state_text}")
            if hasattr(self, 'dio_status_label'):
                color = self.colors['error'] if state else self.colors['success']
                self.dio_status_label.config(fg=color)
                print(f"   界面颜色已更新: {color}")

            return True

        except Exception as e:
            print(f"❌ DIO输出控制失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_dio_based_on_fault_detection(self, fault_detected):
        """根据故障检测结果更新DIO输出

        Args:
            fault_detected (bool): True=检测到故障，False=正常状态
        """
        try:
            print(f"🔧 进入DIO控制方法，故障检测结果: {fault_detected}")

            if not self.fault_detection_enabled:
                print("⚠️  故障检测DIO输出功能已禁用")
                print(f"   可通过设置 self.fault_detection_enabled = True 来启用")
                return

            print(f"✅ 故障检测DIO输出功能已启用")

            if fault_detected:
                # 检测到故障，DIO1输出高电平
                print("🚨 检测到故障，尝试设置DIO1为高电平...")
                success = self.set_dio_output(1)
                if success:
                    print("🚨 检测到故障！DIO1输出高电平")
                    # 可以在这里添加其他故障处理逻辑
                    self.log_fault_detection("故障检测", "检测到故障，DIO1输出高电平")
                else:
                    print("❌ DIO1设置为高电平失败")
            else:
                # 正常状态，DIO1输出低电平
                print("✅ 设备状态正常，尝试设置DIO1为低电平...")
                success = self.set_dio_output(0)
                if success:
                    print("✅ 设备状态正常，DIO1输出低电平")
                    self.log_fault_detection("正常状态", "设备状态正常，DIO1输出低电平")
                else:
                    print("❌ DIO1设置为低电平失败")

        except Exception as e:
            print(f"❌ 根据故障检测更新DIO失败: {e}")
            import traceback
            traceback.print_exc()

    def log_fault_detection(self, status, message):
        """记录故障检测日志"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_message = f"[{timestamp}] 故障检测: {status} - {message}\n"

            # 写入日志文件
            fault_log_file = os.path.join(self.work_dir, "fault_detection_log.txt")
            with open(fault_log_file, 'a', encoding='utf-8') as f:
                f.write(log_message)

        except Exception as e:
            print(f"记录故障检测日志失败: {e}")

    def init_deep_learning(self):
        """初始化深度学习相关参数"""
        # 深度学习模型参数
        self.dl_params = {
            'sample_length': 1024,      # 样本长度
            'stride': 512,              # 步长
            'overlap_rate': 0.5,        # 重叠率
            'samples_per_class': 100,   # 每类样本数
            'train_ratio': 0.7,         # 训练集比例
            'valid_ratio': 0.2,         # 验证集比例
            'test_ratio': 0.1,          # 测试集比例
            'batch_size': 32,           # 训练批次
            'learning_rate': 0.001,     # 学习率
            'epochs': 50,               # 训练轮次
            'dropout': 0.5,             # Dropout率
        }

        # 深度学习状态
        self.dl_model = None
        self.dl_training = False
        self.dl_data_loaded = False
        self.dl_train_data = None
        self.dl_valid_data = None
        self.dl_test_data = None
        self.dl_history = None

        # 深度学习UI变量
        self.dl_data_dir_var = StringVar(value="")
        self.dl_sample_length_var = StringVar(value=str(self.dl_params['sample_length']))
        self.dl_stride_var = StringVar(value=str(self.dl_params['stride']))
        self.dl_overlap_rate_var = StringVar(value=str(self.dl_params['overlap_rate']))
        self.dl_samples_per_class_var = StringVar(value=str(self.dl_params['samples_per_class']))
        self.dl_train_ratio_var = StringVar(value=str(self.dl_params['train_ratio']))
        self.dl_valid_ratio_var = StringVar(value=str(self.dl_params['valid_ratio']))
        self.dl_test_ratio_var = StringVar(value=str(self.dl_params['test_ratio']))
        self.dl_batch_size_var = StringVar(value=str(self.dl_params['batch_size']))
        self.dl_learning_rate_var = StringVar(value=str(self.dl_params['learning_rate']))
        self.dl_epochs_var = StringVar(value=str(self.dl_params['epochs']))
        self.dl_dropout_var = StringVar(value=str(self.dl_params['dropout']))
        self.dl_status_var = StringVar(value="未加载数据")

        # 模型保存路径
        self.dl_model_save_path = os.path.join(self.work_dir, "models")
        if not os.path.exists(self.dl_model_save_path):
            os.makedirs(self.dl_model_save_path)

    def update_filename_preview(self):
        """更新Excel文件名预览"""
        try:
            # 获取当前时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 构建文件名：Vibration_[轴承ID]_[型号]_[转速rpm]_[采样率Hz]_[采样点数pts]_[滚动体数]_[滚动体直径]_[节径直径]_[接触角]_[时间戳].xlsx
            filename = (f"Vibration_{self.daq_bearing_id_var.get()}_{self.daq_bearing_model_var.get()}_"
                       f"{self.daq_rpm_var.get()}rpm_{self.daq_sample_rate_var.get()}Hz_"
                       f"{self.daq_num_points_var.get()}pts_{self.daq_ball_count_var.get()}balls_"
                       f"{self.daq_ball_diameter_var.get()}mm_{self.daq_pitch_diameter_var.get()}mm_"
                       f"{self.daq_contact_angle_var.get()}deg_{timestamp}.xlsx")

            self.daq_filename_preview_var.set(filename)
        except Exception as e:
            self.daq_filename_preview_var.set(f"文件名生成错误: {str(e)}")

    def draw_bearing_pattern(self, draw, x, y):
        """绘制轴承图案"""
        try:
            color = '#cbd5e1'  # 浅灰色
            # 外圈
            draw.ellipse([x-80, y-80, x+80, y+80], outline=color, width=2)
            # 内圈
            draw.ellipse([x-40, y-40, x+40, y+40], outline=color, width=2)
            # 滚动体
            for i in range(8):
                angle = i * 45 * 3.14159 / 180
                ball_x = x + 60 * np.cos(angle)
                ball_y = y + 60 * np.sin(angle)
                draw.ellipse([ball_x-8, ball_y-8, ball_x+8, ball_y+8],
                           outline=color, width=1)
        except Exception as e:
            print(f"绘制轴承图案失败: {e}")

    def draw_gear_pattern(self, draw, x, y):
        """绘制齿轮图案"""
        try:
            color = '#cbd5e1'
            # 齿轮外圈
            draw.ellipse([x-60, y-60, x+60, y+60], outline=color, width=2)
            # 齿轮内圈
            draw.ellipse([x-20, y-20, x+20, y+20], outline=color, width=2)
            # 齿轮齿
            for i in range(12):
                angle = i * 30 * 3.14159 / 180
                start_x = x + 50 * np.cos(angle)
                start_y = y + 50 * np.sin(angle)
                end_x = x + 70 * np.cos(angle)
                end_y = y + 70 * np.sin(angle)
                draw.line([start_x, start_y, end_x, end_y], fill=color, width=2)
        except Exception as e:
            print(f"绘制齿轮图案失败: {e}")

    def draw_wave_pattern(self, draw, x, y):
        """绘制波形图案"""
        try:
            color = '#cbd5e1'
            # 绘制正弦波
            points = []
            for i in range(200):
                wave_x = x - 100 + i
                wave_y = y + 20 * np.sin(i * 0.1)
                points.append((wave_x, wave_y))

            for i in range(len(points) - 1):
                draw.line([points[i], points[i+1]], fill=color, width=1)
        except Exception as e:
            print(f"绘制波形图案失败: {e}")

    def init_log_files(self):
        """初始化日志文件，确保日志目录存在"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 确保会话日志文件存在
            if not os.path.exists(self.log_file):
                with open(self.log_file, 'w', encoding='utf-8') as f:
                    f.write(f"# DeepSeek故障诊断助手对话记录 - 创建于 {timestamp}\n\n")

            # 确保特征日志文件存在
            if not os.path.exists(self.mat_feature_log_file):
                with open(self.mat_feature_log_file, 'w', encoding='utf-8') as f:
                    f.write(f"# DeepSeek故障诊断助手特征记录 - 创建于 {timestamp}\n\n")

            # 避免在__init__过程中调用self.log_message，使用延迟初始化
            def delayed_log_init():
                try:
                    with open(self.log_file, 'a', encoding='utf-8') as f:
                        f.write(f"[{timestamp}] 系统:\n日志系统初始化完成\n\n")
                        f.flush()
                except Exception as e:
                    print(f"延迟日志初始化失败: {str(e)}")

            # 使用after方法延迟执行
            self.master.after(1000, delayed_log_init)

        except Exception as e:
            print(f"初始化日志文件失败: {str(e)}")
            # 使用after方法延迟显示消息框，避免在__init__过程中弹出
            self.master.after(500, lambda: messagebox.showwarning("日志初始化警告",
                                  f"无法初始化日志文件\n\n错误信息: {str(e)}\n\n部分功能可能受到影响。"))

    # ============== 启动画面 ==============
    def show_splash(self):
        """显示企业级启动进度画面"""
        self.splash = Toplevel(self.master)
        self.splash.overrideredirect(True)
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()
        width, height = 500, 300
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.splash.geometry(f"{width}x{height}+{x}+{y}")

        # 设置启动画面背景色
        self.splash.configure(bg=self.colors['surface'])

        # 创建主框架
        main_frame = Frame(self.splash, bg=self.colors['surface'], relief='raised', bd=2)
        main_frame.pack(fill=BOTH, expand=True, padx=2, pady=2)

        # 顶部蓝色条
        top_bar = Frame(main_frame, bg=self.colors['primary'], height=60)
        top_bar.pack(fill=X, pady=(0, 20))
        top_bar.pack_propagate(False)

        # 公司/产品标题
        title_frame = Frame(top_bar, bg=self.colors['primary'])
        title_frame.pack(expand=True, fill=BOTH)

        Label(title_frame, text="智能故障诊断系统",
              font=("微软雅黑", 16, "bold"),
              fg='white', bg=self.colors['primary']).pack(pady=8)
        Label(title_frame, text="DeepSeek Intelligent Fault Diagnosis System",
              font=("Arial", 10),
              fg='#e2e8f0', bg=self.colors['primary']).pack()

        # 中间内容区域
        content_frame = Frame(main_frame, bg=self.colors['surface'])
        content_frame.pack(fill=BOTH, expand=True, padx=20)

        # 版本信息
        Label(content_frame, text="版本 v1.0 Enterprise Edition",
              font=("微软雅黑", 10),
              fg=self.colors['text_secondary'], bg=self.colors['surface']).pack(pady=(10, 5))

        # 状态标签
        self.splash_status = Label(content_frame, text="正在初始化系统组件...",
                                  font=("微软雅黑", 10),
                                  fg=self.colors['text_primary'], bg=self.colors['surface'])
        self.splash_status.pack(pady=10)

        # 进度条
        progress_frame = Frame(content_frame, bg=self.colors['surface'])
        progress_frame.pack(fill=X, pady=10)

        self.progress = ttk.Progressbar(progress_frame, length=400, mode="determinate",
                                       style="Custom.Horizontal.TProgressbar")
        self.progress.pack()
        self.progress["maximum"] = 100
        self.progress["value"] = 0

        # 底部信息
        bottom_frame = Frame(main_frame, bg=self.colors['surface'])
        bottom_frame.pack(fill=X, pady=(10, 20))

        Label(bottom_frame, text="© 2024 智能诊断技术有限公司",
              font=("微软雅黑", 8),
              fg=self.colors['text_secondary'], bg=self.colors['surface']).pack()

        self.update_splash_progress(10, "正在加载配置文件...")

    def update_splash_progress(self, value, text=""):
        """更新启动进度"""
        self.progress["value"] = value
        if text and hasattr(self, 'splash_status'):
            self.splash_status.config(text=text)
        self.master.update()

    def close_splash(self):
        """关闭启动画面"""
        self.splash.destroy()

    # ============== 设置管理 ==============
    def load_settings(self):
        """加载设置"""
        self.update_splash_progress(20, "加载设置...")
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, "r", encoding="utf-8") as f:
                    settings = json.load(f)
                    self.key = settings.get("api_key", "sk-29cf258984c34710a70de5bef50a41e4")
                    self.api_url = settings.get("api_url", "https://api.deepseek.com")
            else:
                self.key = "sk-29cf258984c34710a70de5bef50a41e4"
                self.api_url = "https://api.deepseek.com"
                self.save_settings()
        except Exception as e:
            self.key = "sk-29cf258984c34710a70de5bef50a41e4"
            self.api_url = "https://api.deepseek.com"
            print(f"加载设置失败: {e}")

    def save_settings(self):
        """保存设置"""
        try:
            settings = {"api_key": self.key, "api_url": self.api_url}
            with open(self.settings_file, "w", encoding="utf-8") as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            print(f"保存设置失败: {e}")

    def show_settings(self):
        """显示设置对话框"""
        settings_window = Toplevel(self.master)
        settings_window.title("设置")
        settings_window.geometry("500x300")
        settings_window.transient(self.master)
        settings_window.grab_set()

        ttk.Label(settings_window, text="API设置", font=("微软雅黑", 12, "bold")).grid(row=0, column=0, columnspan=2, pady=10, padx=10, sticky=W)
        ttk.Label(settings_window, text="API Key:").grid(row=1, column=0, padx=10, pady=5, sticky=W)
        api_key_var = StringVar(value=self.key)
        ttk.Entry(settings_window, textvariable=api_key_var, width=40).grid(row=1, column=1, padx=10, pady=5, sticky=W+E)
        ttk.Label(settings_window, text="API URL:").grid(row=2, column=0, padx=10, pady=5, sticky=W)
        api_url_var = StringVar(value=self.api_url)
        ttk.Entry(settings_window, textvariable=api_url_var, width=40).grid(row=2, column=1, padx=10, pady=5, sticky=W+E)
        ttk.Label(settings_window, text="主题设置", font=("微软雅黑", 12, "bold")).grid(row=3, column=0, columnspan=2, pady=10, padx=10, sticky=W)
        ttk.Label(settings_window, text="界面主题:").grid(row=4, column=0, padx=10, pady=5, sticky=W)
        theme_var = StringVar(value=self.theme.get())
        theme_combo = ttk.Combobox(settings_window, textvariable=theme_var, values=["default", "light", "dark"], state="readonly", width=10)
        theme_combo.grid(row=4, column=1, padx=10, pady=5, sticky=W)
        def save_and_close():
            self.key = api_key_var.get()
            self.api_url = api_url_var.get()
            self.save_settings()
            if theme_var.get() != self.theme.get():
                self.theme.set(theme_var.get())
                self.apply_theme(theme_var.get())
            settings_window.destroy()
        ttk.Button(settings_window, text="保存", command=save_and_close).grid(row=6, column=0, pady=20, padx=10)
        ttk.Button(settings_window, text="取消", command=settings_window.destroy).grid(row=6, column=1, pady=20, padx=10)

    def apply_theme(self, theme_name):
        """应用主题"""
        style = ttk.Style()
        if theme_name == "light":
            style.theme_use("clam")
            self.master.configure(bg="#f0f0f0")
            style.configure("TLabelframe", background="#f0f0f0")
            style.configure("TLabelframe.Label", background="#f0f0f0")
            style.configure("TFrame", background="#f0f0f0")
            style.configure("TButton", background="#e0e0e0")
            self.input_text.configure(bg="white", fg="black")
            self.history_text.configure(bg="white", fg="black")
        elif theme_name == "dark":
            style.theme_use("clam")
            self.master.configure(bg="#333333")
            style.configure("TLabelframe", background="#333333")
            style.configure("TLabelframe.Label", background="#333333", foreground="white")
            style.configure("TFrame", background="#333333")
            style.configure("TButton", background="#555555")
            style.configure("TLabel", background="#333333", foreground="white")
            self.input_text.configure(bg="#3e3e3e", fg="white", insertbackground="white")
            self.history_text.configure(bg="#3e3e3e", fg="white")
        else:
            style.theme_use("default")
            self.master.configure(bg=self.master.cget("bg"))
            self.input_text.configure(bg="white", fg="black")
            self.history_text.configure(bg="white", fg="black")

    # ============== API连接检查 ==============
    def check_api_connection(self):
        """检查API连接状态"""
        self.status.set("正在检查API连接...")
        threading.Thread(target=self._check_api_connection, daemon=True).start()

    def load_api_config(self):
        """从配置文件加载API参数"""
        default_params = {
            'temperature': 0.4,
            'top_p': 0.85,
            'max_tokens': 8000,
            'frequency_penalty': 0.3,
            'presence_penalty': 0.2
        }

        try:
            if os.path.exists(self.api_config_file):
                with open(self.api_config_file, 'r', encoding='utf-8') as f:
                    saved_params = json.load(f)
                    # 验证参数有效性并确保类型正确
                    for key, value in saved_params.items():
                        if key in default_params:
                            # 确保max_tokens是整数类型
                            if key == 'max_tokens':
                                default_params[key] = int(value)
                            else:
                                default_params[key] = float(value)
                print(f"✅ 已加载API配置: {saved_params}")
            else:
                print("📋 使用默认API配置")
        except Exception as e:
            print(f"❌ 加载API配置失败，使用默认配置: {str(e)}")

        return default_params

    def save_api_config(self):
        """保存API参数到配置文件"""
        try:
            # 确保max_tokens是整数类型
            params_to_save = self.api_params.copy()
            if 'max_tokens' in params_to_save:
                params_to_save['max_tokens'] = int(params_to_save['max_tokens'])

            with open(self.api_config_file, 'w', encoding='utf-8') as f:
                json.dump(params_to_save, f, indent=2, ensure_ascii=False)
            print(f"✅ API配置已保存到: {self.api_config_file}")
            return True
        except Exception as e:
            print(f"❌ 保存API配置失败: {str(e)}")
            return False

    def _check_api_connection(self):
        """执行API连接检查"""
        try:
            client = OpenAI(api_key=self.key, base_url=self.api_url)
            response = client.chat.completions.create(
                model="deepseek-reasoner",
                messages=[{"role": "user", "content": "测试连接"}],
                temperature=self.api_params['temperature'],
                max_tokens=100,         # 测试连接只需要简短回复
                top_p=self.api_params['top_p'],
                frequency_penalty=self.api_params['frequency_penalty'],
                presence_penalty=self.api_params['presence_penalty'],
                stream=False
            )
            self.set_status("API连接正常，系统就绪")
            # 更新连接状态指示器
            if hasattr(self, 'connection_status'):
                self.connection_status.config(text="● 连接正常", fg=self.colors['success'])
        except Exception as e:
            error_msg = str(e)
            if "authentication" in error_msg.lower():
                status_text = "API密钥无效，请检查设置"
                if hasattr(self, 'connection_status'):
                    self.connection_status.config(text="● 认证失败", fg=self.colors['error'])
            elif "connection" in error_msg.lower():
                status_text = "API连接失败，请检查网络"
                if hasattr(self, 'connection_status'):
                    self.connection_status.config(text="● 连接失败", fg=self.colors['error'])
            else:
                status_text = f"API检查失败: {error_msg[:50]}"
                if hasattr(self, 'connection_status'):
                    self.connection_status.config(text="● 连接异常", fg=self.colors['warning'])
            self.set_status(status_text)

    # ============== OCR 配置 ==============
    def configure_ocr(self):
        """配置OCR引擎"""
        self.update_splash_progress(40, "配置OCR...")
        try:
            if sys.platform == 'win32':
                ocr_path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
                if not os.path.exists(ocr_path):
                    raise FileNotFoundError(f"Tesseract未安装在默认路径: {ocr_path}")
                pytesseract.pytesseract.tesseract_cmd = ocr_path
                os.environ['TESSDATA_PREFIX'] = r'C:\Program Files\Tesseract-OCR\tessdata'
            pytesseract.get_tesseract_version()
        except Exception as e:
            messagebox.showerror("OCR错误", f"OCR配置失败: {str(e)}")
            sys.exit(1)

    # ============== GUI布局 ==============
    def setup_ui(self):
        """设置企业级用户界面"""
        self.update_splash_progress(60, "初始化界面组件...")

        # 设置窗口标题和图标
        self.master.title("智能故障诊断系统 - DeepSeek Enterprise Edition")
        self.master.geometry("1200x820")
        self.master.minsize(1000, 700)

        # 设置窗口背景
        if hasattr(self, 'background_image') and self.background_image:
            bg_label = Label(self.master, image=self.background_image)
            bg_label.place(x=0, y=0, relwidth=1, relheight=1)
        else:
            self.master.configure(bg=self.colors['background'])

        # 应用企业级样式
        self.setup_enterprise_style()

        # 创建菜单
        self.create_menu()

        # 创建主框架
        main_frame = Frame(self.master, bg='white', relief='raised', bd=1)
        main_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)

        # 创建标题栏
        self.create_title_bar(main_frame)

        # 创建内容区域 - 左右分栏布局
        content_frame = Frame(main_frame, bg='white')
        content_frame.pack(fill=BOTH, expand=True, pady=(10, 0))

        # 左侧面板 - 问答交互区域（占40%宽度）- 保持不变
        left_panel = Frame(content_frame, bg='white', width=480)
        left_panel.pack(side=LEFT, fill=BOTH, padx=(0, 5))
        left_panel.pack_propagate(False)

        # 右侧面板 - 可切换的功能区域（占60%宽度）
        right_panel = Frame(content_frame, bg='white')
        right_panel.pack(side=RIGHT, fill=BOTH, expand=True, padx=(5, 0))

        # 设置左侧UI组件 - 这些保持不变
        self.setup_attachment_ui(left_panel)
        self.setup_input_ui(left_panel)
        self.setup_history_ui(left_panel)

        # 在右侧面板创建功能选择器和可切换内容
        self.setup_right_panel_with_selector(right_panel)

        # 创建状态栏
        self.create_status_bar()

    def setup_right_panel_with_selector(self, parent):
        """设置右侧面板，包含功能选择器和可切换内容"""
        # 创建功能选择按钮栏
        selector_frame = Frame(parent, bg=self.colors['accent'], height=40)
        selector_frame.pack(fill=X, pady=(0, 5))
        selector_frame.pack_propagate(False)

        # 功能选择按钮
        buttons_frame = Frame(selector_frame, bg=self.colors['accent'])
        buttons_frame.pack(side=LEFT, padx=15, pady=8)

        # 数据分析按钮
        self.data_analysis_btn = Button(buttons_frame, text="📊 数据分析",
                                       font=("微软雅黑", 10, "bold"),
                                       bg=self.colors['primary'], fg='white',
                                       relief='flat', padx=15, pady=5,
                                       command=lambda: self.show_right_panel("data_analysis"))
        self.data_analysis_btn.pack(side=LEFT, padx=(0, 8))

        # 深度学习诊断按钮
        self.deep_learning_btn = Button(buttons_frame, text="🧠 深度学习诊断",
                                       font=("微软雅黑", 10, "bold"),
                                       bg=self.colors['surface'], fg=self.colors['text_primary'],
                                       relief='flat', padx=15, pady=5,
                                       command=lambda: self.show_right_panel("deep_learning"))
        self.deep_learning_btn.pack(side=LEFT, padx=(0, 8))

        # 状态指示器
        status_frame = Frame(selector_frame, bg=self.colors['accent'])
        status_frame.pack(side=RIGHT, padx=15, pady=8)

        Label(status_frame, text="当前:",
              font=("微软雅黑", 9, "bold"),
              fg=self.colors['primary'], bg=self.colors['accent']).pack(side=LEFT)

        self.current_function_label = Label(status_frame, text="数据分析",
                                          font=("微软雅黑", 9, "bold"),
                                          fg=self.colors['gradient_start'], bg=self.colors['accent'])
        self.current_function_label.pack(side=LEFT, padx=(5, 0))

        # 创建可切换的内容区域
        self.right_content_frame = Frame(parent, bg='white')
        self.right_content_frame.pack(fill=BOTH, expand=True)

        # 初始化功能面板
        self.current_function = "data_analysis"  # 默认显示数据分析
        self.right_panels = {}

        # 创建数据分析面板
        self.create_data_analysis_right_panel()

        # 创建深度学习诊断面板
        self.create_deep_learning_right_panel()

        # 显示默认面板
        self.show_right_panel("data_analysis")

    def show_right_panel(self, panel_name):
        """显示指定的右侧面板"""
        # 隐藏所有右侧面板
        for frame in self.right_panels.values():
            frame.pack_forget()

        # 显示指定面板
        if panel_name in self.right_panels:
            self.right_panels[panel_name].pack(fill=BOTH, expand=True)
            self.current_function = panel_name

            # 更新按钮状态
            if panel_name == "data_analysis":
                self.data_analysis_btn.config(bg=self.colors['primary'], fg='white')
                self.deep_learning_btn.config(bg=self.colors['surface'], fg=self.colors['text_primary'])
                self.current_function_label.config(text="数据分析")
            elif panel_name == "deep_learning":
                self.data_analysis_btn.config(bg=self.colors['surface'], fg=self.colors['text_primary'])
                self.deep_learning_btn.config(bg=self.colors['primary'], fg='white')
                self.current_function_label.config(text="深度学习诊断")

    def create_data_analysis_right_panel(self):
        """创建数据分析右侧面板"""
        # 创建数据分析框架
        data_analysis_frame = Frame(self.right_content_frame, bg='white')
        self.right_panels["data_analysis"] = data_analysis_frame

        # 设置右侧可视化区域
        self.setup_visualization_panel(data_analysis_frame)

        # 设置数据采集控制面板 - 限制高度
        self.setup_data_acquisition_panel(data_analysis_frame)

    def create_deep_learning_right_panel(self):
        """创建深度学习诊断右侧面板"""
        if not DL_AVAILABLE:
            # 如果深度学习库不可用，显示提示信息
            dl_frame = Frame(self.right_content_frame, bg='white')
            self.right_panels["deep_learning"] = dl_frame

            Label(dl_frame, text="🚫 深度学习功能不可用",
                  font=("微软雅黑", 16, "bold"),
                  fg=self.colors['error'], bg='white').pack(expand=True)

            Label(dl_frame, text="请安装以下依赖：\n• torch\n• scikit-learn\n• matplotlib",
                  font=("微软雅黑", 12),
                  fg=self.colors['text_secondary'], bg='white').pack(expand=True)
            return

        # 创建深度学习主框架
        dl_frame = Frame(self.right_content_frame, bg='white')
        self.right_panels["deep_learning"] = dl_frame

        # 创建标题栏
        title_frame = Frame(dl_frame, bg=self.colors['primary'], height=50)
        title_frame.pack(fill=X, pady=(0, 8))
        title_frame.pack_propagate(False)

        Label(title_frame, text="🧠 深度学习故障诊断系统",
              font=("微软雅黑", 14, "bold"),
              fg='white', bg=self.colors['primary']).pack(side=LEFT, padx=15, pady=12)

        # 状态指示器
        self.dl_status_label = Label(title_frame, textvariable=self.dl_status_var,
                                   font=("微软雅黑", 10),
                                   fg=self.colors['accent_bright'], bg=self.colors['primary'])
        self.dl_status_label.pack(side=RIGHT, padx=15, pady=12)

        # 创建选项卡
        self.create_dl_notebook_embedded(dl_frame)

    def create_dl_notebook_embedded(self, parent):
        """创建嵌入式深度学习选项卡"""
        # 创建选项卡控件
        self.dl_notebook = ttk.Notebook(parent)
        self.dl_notebook.pack(fill=BOTH, expand=True)

        # 1. 数据加载选项卡
        self.create_data_loading_tab()

        # 2. 数据预处理选项卡
        self.create_data_preprocessing_tab()

        # 3. 模型设置选项卡
        self.create_model_settings_tab()

        # 4. 模型训练选项卡
        self.create_model_training_tab()

        # 5. 模型评估选项卡
        self.create_model_evaluation_tab()

        # 6. 故障预测选项卡
        self.create_fault_prediction_tab()

    def setup_visualization_panel(self, parent):
        """设置右侧可视化面板 - 科技感设计"""
        # 主框架 - 科技感背景
        viz_frame = Frame(parent, bg=self.colors['surface'], relief='flat', bd=0)
        viz_frame.pack(fill=BOTH, expand=True)

        # 科技感标题栏
        title_frame = Frame(viz_frame, bg=self.colors['accent'], height=40)
        title_frame.pack(fill=X)
        title_frame.pack_propagate(False)

        Label(title_frame, text="📊 数据分析",
              font=("微软雅黑", 14, "bold"),
              fg=self.colors['primary'], bg=self.colors['accent']).pack(side=LEFT, padx=15, pady=10)

        # 附件选择下拉框
        attachment_frame = Frame(title_frame, bg=self.colors['accent'])
        attachment_frame.pack(side=LEFT, padx=20, pady=8)

        Label(attachment_frame, text="文件:",
              font=("微软雅黑", 10, "bold"),
              fg=self.colors['primary'], bg=self.colors['accent']).pack(side=LEFT)

        self.attachment_var = StringVar()
        self.attachment_combo = ttk.Combobox(attachment_frame, textvariable=self.attachment_var,
                                           width=12, state="readonly", font=("Consolas", 8))
        self.attachment_combo.pack(side=LEFT, padx=5)
        self.attachment_combo.bind('<<ComboboxSelected>>', self.on_attachment_selected)

        # 科技感可视化按钮
        viz_buttons_frame = Frame(title_frame, bg=self.colors['accent'])
        viz_buttons_frame.pack(side=RIGHT, padx=15, pady=8)

        for text, cmd in [("📈 时域", 'time'), ("📊 频域", 'freq'), ("🌊 小波", 'wavelet')]:
            Button(viz_buttons_frame, text=text,
                   font=("微软雅黑", 9, "bold"),
                   bg=self.colors['surface'], fg=self.colors['text_primary'],
                   relief='flat', padx=8, pady=3,
                   command=lambda c=cmd: self.show_visualization_type(c)).pack(side=LEFT, padx=1)

        # 主内容区域 - 左右分栏
        content_container = Frame(viz_frame, bg=self.colors['surface'])
        content_container.pack(fill=BOTH, expand=True, padx=8, pady=8)

        # 左侧：数据可视化区域（85%宽度，增加显示空间）
        viz_area = Frame(content_container, bg=self.colors['surface_light'])
        viz_area.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 4))

        self.viz_content_frame = Frame(viz_area, bg=self.colors['surface_light'])
        self.viz_content_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # 右侧：计算指标和诊断结果面板（15%宽度，缩小指标区域）
        self.setup_metrics_and_diagnosis_panel(content_container)

    def setup_metrics_and_diagnosis_panel(self, parent):
        """设置计算指标和诊断结果面板 - 紧凑布局"""
        # 指标面板主框架 - 右侧垂直布局，缩小宽度以节省空间
        metrics_frame = Frame(parent, bg=self.colors['surface_light'], width=280)  # 从500减少到280
        metrics_frame.pack(side=RIGHT, fill=Y, padx=(4, 0))
        metrics_frame.pack_propagate(False)

        # 指标面板标题 - 缩小高度
        metrics_title = Frame(metrics_frame, bg=self.colors['accent'], height=25)  # 从30减少到25
        metrics_title.pack(fill=X)
        metrics_title.pack_propagate(False)

        Label(metrics_title, text="🔬 特征指标",  # 缩短标题文字
              font=("微软雅黑", 9, "bold"),  # 减小字体
              fg=self.colors['primary'], bg=self.colors['accent']).pack(side=LEFT, padx=6, pady=2)  # 减小间距

        # 指标计算按钮 - 缩小尺寸
        calc_btn = Button(metrics_title, text="⚡",  # 只显示图标
                         font=("微软雅黑", 8, "bold"),  # 减小字体
                         bg=self.colors['success'], fg=self.colors['primary'],
                         relief='flat', padx=4, pady=1,  # 减小按钮尺寸
                         command=self.calculate_metrics)
        calc_btn.pack(side=RIGHT, padx=4, pady=2)  # 减小间距

        # 添加工具提示
        def create_tooltip(widget, text):
            def on_enter(event):
                tooltip = Toplevel()
                tooltip.wm_overrideredirect(True)
                tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
                label = Label(tooltip, text=text, background="lightyellow",
                             font=("微软雅黑", 8), relief="solid", borderwidth=1)
                label.pack()
                widget.tooltip = tooltip

            def on_leave(event):
                if hasattr(widget, 'tooltip'):
                    widget.tooltip.destroy()
                    del widget.tooltip

            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)

        create_tooltip(calc_btn, "计算特征指标")

        # 创建指标显示区域 - 减小间距
        main_content = Frame(metrics_frame, bg=self.colors['surface'])
        main_content.pack(fill=BOTH, expand=True, padx=2, pady=2)  # 减小间距

        # 指标显示区域（占全部宽度）
        self.create_metrics_column(main_content)

    def create_metrics_column(self, parent):
        """创建指标显示区域"""
        # 指标显示框架 - 占用全部宽度
        metrics_column = Frame(parent, bg=self.colors['surface_light'])
        metrics_column.pack(fill=BOTH, expand=True)

        # 创建滚动区域
        canvas = Canvas(metrics_column, bg=self.colors['surface_light'])
        scrollbar = Scrollbar(metrics_column, orient="vertical", command=canvas.yview)
        scrollable_frame = Frame(canvas, bg=self.colors['surface_light'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 绑定鼠标滚轮事件
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        canvas.bind("<MouseWheel>", on_mousewheel)
        scrollable_frame.bind("<MouseWheel>", on_mousewheel)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 创建指标显示区域
        self.create_all_metrics_sections(scrollable_frame)



    def create_all_metrics_sections(self, parent):
        """创建所有指标显示区域 - 重新设计布局"""
        # 创建主容器，使用网格布局，减少边距
        main_container = Frame(parent, bg=self.colors['surface_light'])
        main_container.pack(fill=BOTH, expand=True, padx=0, pady=0)  # 去掉边距以减少空白

        # 时域指标区域 - 卡片式设计，减少间距
        time_card = Frame(main_container, bg=self.colors['surface'], relief='flat', bd=1)
        time_card.pack(fill=X, pady=(0, 0))  # 减少垂直间距

        # 时域标题 - 增大高度和字体
        time_header = Frame(time_card, bg=self.colors['primary'], height=18)  # 从14增加到18
        time_header.pack(fill=X)
        time_header.pack_propagate(False)

        Label(time_header, text="⏱️ 时域指标",
              font=("微软雅黑", 8, "bold"),  # 从6增加到8
              fg='white', bg=self.colors['primary']).pack(pady=1)

        # 时域指标内容区域 - 使用网格布局，减少内边距
        self.time_metrics_frame = Frame(time_card, bg=self.colors['surface'])
        self.time_metrics_frame.pack(fill=X, padx=2, pady=2)  # 增加内边距以容纳更大字体

        # 频域指标区域 - 卡片式设计，减少间距
        freq_card = Frame(main_container, bg=self.colors['surface'], relief='flat', bd=1)
        freq_card.pack(fill=X, pady=(0, 0))  # 减少垂直间距

        # 频域标题 - 增大高度和字体
        freq_header = Frame(freq_card, bg=self.colors['accent'], height=18)  # 从14增加到18
        freq_header.pack(fill=X)
        freq_header.pack_propagate(False)

        Label(freq_header, text="📊 频域指标",
              font=("微软雅黑", 8, "bold"),  # 从6增加到8
              fg=self.colors['primary'], bg=self.colors['accent']).pack(pady=1)

        # 频域指标内容区域 - 增加内边距
        self.freq_metrics_frame = Frame(freq_card, bg=self.colors['surface'])
        self.freq_metrics_frame.pack(fill=X, padx=2, pady=2)  # 增加内边距

        # 故障指标区域 - 卡片式设计，减少间距
        fault_card = Frame(main_container, bg=self.colors['surface'], relief='flat', bd=1)
        fault_card.pack(fill=X, pady=(0, 0))  # 减少垂直间距

        # 故障标题 - 增大高度和字体
        fault_header = Frame(fault_card, bg=self.colors['warning'], height=18)  # 从14增加到18
        fault_header.pack(fill=X)
        fault_header.pack_propagate(False)

        Label(fault_header, text="⚠️ 故障指标",
              font=("微软雅黑", 8, "bold"),  # 从6增加到8
              fg='white', bg=self.colors['warning']).pack(pady=1)

        # 故障指标内容区域 - 增加内边距
        self.fault_metrics_frame = Frame(fault_card, bg=self.colors['surface'])
        self.fault_metrics_frame.pack(fill=X, padx=2, pady=2)  # 增加内边距

        # 风机参数区域 - 卡片式设计
        wind_card = Frame(main_container, bg=self.colors['surface'], relief='flat', bd=1)
        wind_card.pack(fill=X, pady=(2, 0))  # 添加顶部间距

        # 风机参数标题
        wind_header = Frame(wind_card, bg=self.colors['accent'], height=18)
        wind_header.pack(fill=X)
        wind_header.pack_propagate(False)

        Label(wind_header, text="🌪️ 风机参数",
              font=("微软雅黑", 8, "bold"),
              fg='white', bg=self.colors['accent']).pack(pady=1)

        # 风机参数内容区域
        self.wind_params_frame = Frame(wind_card, bg=self.colors['surface'])
        self.wind_params_frame.pack(fill=X, padx=2, pady=2)

        # 创建风机参数按钮
        self.create_wind_params_buttons()

        # 初始化显示
        self.show_initial_metrics()

    def create_wind_params_buttons(self):
        """创建风机参数按钮"""
        # 风机参数按钮
        wind_params_btn = Button(self.wind_params_frame,
                               text="⚙️ 风机参数",
                               font=("微软雅黑", 8),
                               bg=self.colors['accent'],
                               fg='white',
                               relief='flat',
                               cursor='hand2',
                               command=lambda: self.show_wind_turbine_params_dialog())
        wind_params_btn.pack(fill=X, pady=1)

        # 特征指标按钮
        wind_features_btn = Button(self.wind_params_frame,
                                 text="📊 特征指标",
                                 font=("微软雅黑", 8),
                                 bg=self.colors['primary'],
                                 fg='white',
                                 relief='flat',
                                 cursor='hand2',
                                 command=self.show_wind_turbine_features)
        wind_features_btn.pack(fill=X, pady=1)

        # 添加按钮悬停效果
        def on_enter_params(e):
            wind_params_btn.config(bg=self.colors['primary'])
        def on_leave_params(e):
            wind_params_btn.config(bg=self.colors['accent'])
        def on_enter_features(e):
            wind_features_btn.config(bg=self.colors['accent'])
        def on_leave_features(e):
            wind_features_btn.config(bg=self.colors['primary'])

        wind_params_btn.bind("<Enter>", on_enter_params)
        wind_params_btn.bind("<Leave>", on_leave_params)
        wind_features_btn.bind("<Enter>", on_enter_features)
        wind_features_btn.bind("<Leave>", on_leave_features)

    def show_wind_turbine_features(self):
        """显示风机特征指标"""
        if not hasattr(self, 'wind_turbine_fault_freqs') or not self.wind_turbine_fault_freqs:
            messagebox.showinfo("提示", "请先设置风机参数并计算故障特征频率")
            return

        # 创建特征指标显示窗口
        features_window = Toplevel(self.root)
        features_window.title("风机特征指标")
        features_window.geometry("400x300")
        features_window.configure(bg=self.colors['surface'])
        features_window.resizable(False, False)

        # 标题
        title_frame = Frame(features_window, bg=self.colors['primary'], height=40)
        title_frame.pack(fill=X)
        title_frame.pack_propagate(False)

        Label(title_frame, text="🌪️ 风机特征指标",
              font=("微软雅黑", 12, "bold"),
              fg='white', bg=self.colors['primary']).pack(expand=True)

        # 内容区域
        content_frame = Frame(features_window, bg=self.colors['surface'])
        content_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # 显示特征频率
        features_text = Text(content_frame, height=15, width=50,
                           font=("Consolas", 10),
                           bg=self.colors['surface_light'],
                           fg=self.colors['text_primary'])
        features_text.pack(fill=BOTH, expand=True)

        # 添加滚动条
        scrollbar = Scrollbar(content_frame, orient=VERTICAL, command=features_text.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        features_text.config(yscrollcommand=scrollbar.set)

        # 插入特征频率数据
        features_text.insert(END, "风机故障特征频率:\n")
        features_text.insert(END, "=" * 30 + "\n\n")

        for fault_type, freq_value in self.wind_turbine_fault_freqs.items():
            features_text.insert(END, f"{fault_type:15}: {freq_value}\n")

        features_text.config(state=DISABLED)

    def create_metrics_columns(self, parent):
        """创建垂直堆叠的指标显示"""
        # 时域指标区域
        time_col = Frame(parent, bg=self.colors['surface_light'], relief='flat', bd=1)
        time_col.pack(fill=X, pady=(0, 5))

        Label(time_col, text="⏱️ 时域指标",
              font=("微软雅黑", 8, "bold"),
              fg=self.colors['text_accent'], bg=self.colors['surface_light']).pack(pady=0)

        self.time_metrics_frame = Frame(time_col, bg=self.colors['surface_light'])
        self.time_metrics_frame.pack(fill=X, padx=5, pady=2)

        # 频域指标区域
        freq_col = Frame(parent, bg=self.colors['surface_light'], relief='flat', bd=1)
        freq_col.pack(fill=X, pady=(0, 5))

        Label(freq_col, text="📊 频域指标",
              font=("微软雅黑", 8, "bold"),
              fg=self.colors['text_accent'], bg=self.colors['surface_light']).pack(pady=0)

        self.freq_metrics_frame = Frame(freq_col, bg=self.colors['surface_light'])
        self.freq_metrics_frame.pack(fill=X, padx=5, pady=2)

        # 故障指标区域
        fault_col = Frame(parent, bg=self.colors['surface_light'], relief='flat', bd=1)
        fault_col.pack(fill=X, pady=(0, 5))

        Label(fault_col, text="⚠️ 故障指标",
              font=("微软雅黑", 8, "bold"),
              fg=self.colors['text_accent'], bg=self.colors['surface_light']).pack(pady=0)

        self.fault_metrics_frame = Frame(fault_col, bg=self.colors['surface_light'])
        self.fault_metrics_frame.pack(fill=X, padx=5, pady=2)

        # 初始化显示
        self.show_initial_metrics()

    def show_initial_metrics(self):
        """显示初始指标提示 - 网格布局"""
        # 时域指标提示 - 使用网格布局，两列显示，使用缩短的名称
        time_metrics = ["RMS", "峰度", "偏度", "峰值", "脉冲", "裕度"]
        for i, metric in enumerate(time_metrics):
            row = i // 2
            col = i % 2

            metric_label = Label(self.time_metrics_frame, text=f"• {metric}",
                               font=("微软雅黑", 8),  # 从6增加到8
                               fg=self.colors['text_secondary'],
                               bg=self.colors['surface'],
                               anchor='w')
            metric_label.grid(row=row, column=col, sticky='w', padx=3, pady=1)  # 增加间距

        # 配置网格权重
        self.time_metrics_frame.grid_columnconfigure(0, weight=1)
        self.time_metrics_frame.grid_columnconfigure(1, weight=1)

        # 频域指标提示 - 使用网格布局，两列显示，使用缩短的名称
        freq_metrics = ["峰频", "功率", "重心", "RMS频", "频标差"]
        for i, metric in enumerate(freq_metrics):
            row = i // 2
            col = i % 2

            metric_label = Label(self.freq_metrics_frame, text=f"• {metric}",
                               font=("微软雅黑", 8),  # 从6增加到8
                               fg=self.colors['text_secondary'],
                               bg=self.colors['surface'],
                               anchor='w')
            metric_label.grid(row=row, column=col, sticky='w', padx=3, pady=1)  # 增加间距

        # 配置网格权重
        self.freq_metrics_frame.grid_columnconfigure(0, weight=1)
        self.freq_metrics_frame.grid_columnconfigure(1, weight=1)

        # 故障指标提示 - 使用网格布局，两列显示，使用缩短的名称
        fault_metrics = ["外圈", "内圈", "滚动体", "保持架"]
        for i, metric in enumerate(fault_metrics):
            row = i // 2
            col = i % 2

            metric_label = Label(self.fault_metrics_frame, text=f"• {metric}",
                               font=("微软雅黑", 8),  # 从6增加到8
                               fg=self.colors['text_secondary'],
                               bg=self.colors['surface'],
                               anchor='w')
            metric_label.grid(row=row, column=col, sticky='w', padx=3, pady=1)  # 增加间距

        # 配置网格权重
        self.fault_metrics_frame.grid_columnconfigure(0, weight=1)
        self.fault_metrics_frame.grid_columnconfigure(1, weight=1)





    def calculate_metrics(self):
        """计算特征指标"""
        # 查找可分析的数据文件
        analyzable_attachments = []
        for attachment in self.attachments:
            if isinstance(attachment['content'], dict) and "visual_data" in attachment['content']:
                analyzable_attachments.append(attachment)

        if not analyzable_attachments:
            messagebox.showwarning("计算指标", "请先添加包含振动数据的文件（.mat 或 .xlsx 格式）")
            return

        try:
            # 如果有选中的附件且可分析，使用选中的；否则使用第一个可分析的附件
            if (hasattr(self, 'selected_attachment') and self.selected_attachment and
                isinstance(self.selected_attachment['content'], dict) and
                "visual_data" in self.selected_attachment['content']):
                attachment = self.selected_attachment
            else:
                attachment = analyzable_attachments[0]
                self.selected_attachment = attachment

            # 获取数据
            data_dict = attachment['content']['visual_data']
            signal = data_dict["time_signal"]
            fs = data_dict["fs"]

            # 计算各类指标
            time_metrics = self.calculate_time_domain_metrics(signal)
            freq_metrics = self.calculate_frequency_metrics(signal, fs)
            fault_metrics = self.calculate_fault_indicators(signal, fs)

            # 更新指标显示
            self.update_metrics_display(time_metrics, freq_metrics, fault_metrics)

            # 显示计算完成信息
            file_name = attachment['name'][:20] + '...' if len(attachment['name']) > 20 else attachment['name']
            messagebox.showinfo("分析完成",
                f"✅ 特征指标计算完成！\n\n文件: {file_name}\n数据长度: {len(signal):,}\n采样频率: {fs} Hz\n\n已更新指标面板显示")

        except Exception as e:
            messagebox.showerror("计算错误", f"指标计算失败：{str(e)}")

    def calculate_time_domain_metrics(self, signal):
        """计算时域指标"""
        import numpy as np

        # 基本统计量
        mean_val = np.mean(signal)
        std_val = np.std(signal)

        # RMS有效值
        rms = np.sqrt(np.mean(signal**2))

        # 峰度 (四阶中心矩/方差的平方)
        kurtosis = np.mean((signal - mean_val)**4) / (std_val**4)

        # 偏度 (三阶中心矩/标准差的三次方)
        skewness = np.mean((signal - mean_val)**3) / (std_val**3)

        # 峰值因子 (峰值/有效值)
        peak_factor = np.max(np.abs(signal)) / rms

        # 脉冲因子 (峰值/平均绝对值)
        impulse_factor = np.max(np.abs(signal)) / np.mean(np.abs(signal))

        # 裕度因子 (峰值/方根幅值的平方)
        margin_factor = np.max(np.abs(signal)) / (np.mean(np.sqrt(np.abs(signal))))**2

        return {
            'RMS': f"{rms:.3f}",  # 缩短名称，减少小数位
            '峰度': f"{kurtosis:.3f}",
            '偏度': f"{skewness:.3f}",
            '峰值': f"{peak_factor:.3f}",  # 缩短名称
            '脉冲': f"{impulse_factor:.3f}",  # 缩短名称
            '裕度': f"{margin_factor:.3f}"  # 缩短名称
        }

    def calculate_frequency_metrics(self, signal, fs):
        """计算频域指标"""
        import numpy as np
        from scipy import signal as scipy_signal

        # FFT分析
        freqs, psd = scipy_signal.welch(signal, fs, nperseg=min(1024, len(signal)//4))

        # 峰值频率
        peak_freq = freqs[np.argmax(psd)]

        # 频带功率 (0-1000Hz)
        freq_mask = freqs <= min(1000, fs/2)
        band_power = np.sum(psd[freq_mask])

        # 谱重心 (频谱质心)
        spectral_centroid = np.sum(freqs * psd) / np.sum(psd)

        # 均方根频率
        rms_freq = np.sqrt(np.sum((freqs**2) * psd) / np.sum(psd))

        # 频率方差
        freq_variance = np.sum(((freqs - spectral_centroid)**2) * psd) / np.sum(psd)

        # 频率标准差
        freq_std = np.sqrt(freq_variance)

        return {
            '峰频': f"{peak_freq:.1f}Hz",  # 缩短名称和格式
            '功率': f"{band_power:.4f}",  # 缩短名称，减少小数位
            '重心': f"{spectral_centroid:.1f}Hz",  # 缩短名称
            'RMS频': f"{rms_freq:.1f}Hz",  # 缩短名称
            '频标差': f"{freq_std:.1f}Hz"  # 缩短名称
        }

    def calculate_fault_indicators(self, signal, fs):
        """计算故障特征频率指标"""
        import numpy as np

        # 从设置中获取轴承参数，如果没有则使用默认值
        if hasattr(self, 'bearing_params') and self.bearing_params:
            shaft_freq = self.bearing_params.get('shaft_freq', 30.0)
            ball_num = self.bearing_params.get('ball_num', 8)
            contact_angle = self.bearing_params.get('contact_angle', 0)
            pitch_diameter = self.bearing_params.get('pitch_diameter', 50.0)
            ball_diameter = self.bearing_params.get('ball_diameter', 8.0)
        else:
            # 默认轴承参数 (6205轴承)
            shaft_freq = 30.0  # 转轴频率 Hz (1800 RPM)
            ball_num = 8       # 滚动体数量
            contact_angle = 0  # 接触角 (度)
            pitch_diameter = 50.0  # 节圆直径 mm
            ball_diameter = 8.0    # 滚动体直径 mm

        # 计算轴承特征频率
        cos_alpha = np.cos(np.radians(contact_angle))

        # 外圈故障频率 BPFO (Ball Pass Frequency Outer)
        bpfo = (ball_num / 2) * shaft_freq * (1 - (ball_diameter / pitch_diameter) * cos_alpha)

        # 内圈故障频率 BPFI (Ball Pass Frequency Inner)
        bpfi = (ball_num / 2) * shaft_freq * (1 + (ball_diameter / pitch_diameter) * cos_alpha)

        # 滚动体故障频率 BSF (Ball Spin Frequency)
        bsf = (pitch_diameter / (2 * ball_diameter)) * shaft_freq * (1 - ((ball_diameter / pitch_diameter) * cos_alpha)**2)

        # 保持架故障频率 FTF (Fundamental Train Frequency)
        ftf = (shaft_freq / 2) * (1 - (ball_diameter / pitch_diameter) * cos_alpha)

        return {
            '外圈': f"{bpfo:.1f}Hz",  # 进一步缩短名称
            '内圈': f"{bpfi:.1f}Hz",
            '滚动体': f"{bsf:.1f}Hz",
            '保持架': f"{ftf:.1f}Hz"
        }

    def calculate_wind_turbine_fault_frequencies(self, rpm, blade_count=3):
        """
        计算风机故障特征频率

        Args:
            rpm (float): 转子转速 (RPM)
            blade_count (int): 叶片数量，默认3

        Returns:
            dict: 风机故障特征频率字典
        """
        # 转子转频 (Hz)
        rotor_freq = rpm / 60.0

        # 叶片通过频率 BPF (Blade Passing Frequency)
        bpf = rotor_freq * blade_count

        # 转子不平衡频率 (通常为1X转频及其谐波)
        unbalance_1x = rotor_freq
        unbalance_2x = rotor_freq * 2

        # 转子不对中频率 (通常为2X转频及其谐波)
        misalignment_2x = rotor_freq * 2
        misalignment_4x = rotor_freq * 4

        # 叶片故障频率 (叶片通过频率的谐波和边带)
        blade_fault_1x = bpf
        blade_fault_2x = bpf * 2

        return {
            '转子转频': f"{rotor_freq:.2f}Hz",
            '叶片通过': f"{bpf:.2f}Hz",
            '转子不平衡1X': f"{unbalance_1x:.2f}Hz",
            '转子不平衡2X': f"{unbalance_2x:.2f}Hz",
            '转子不对中2X': f"{misalignment_2x:.2f}Hz",
            '转子不对中4X': f"{misalignment_4x:.2f}Hz",
            '叶片故障1X': f"{blade_fault_1x:.2f}Hz",
            '叶片故障2X': f"{blade_fault_2x:.2f}Hz"
        }

    def update_metrics_display(self, time_metrics, freq_metrics, fault_metrics):
        """更新指标显示 - 网格布局优化"""
        # 保存当前指标数据，用于字体大小调整
        self.current_time_metrics = time_metrics
        self.current_freq_metrics = freq_metrics
        self.current_fault_metrics = fault_metrics

        # 清空现有显示
        for widget in self.time_metrics_frame.winfo_children():
            widget.destroy()
        for widget in self.freq_metrics_frame.winfo_children():
            widget.destroy()
        for widget in self.fault_metrics_frame.winfo_children():
            widget.destroy()

        # 计算适合的字体大小 - 增大字体，减少空白
        try:
            # 获取面板宽度
            panel_width = self.time_metrics_frame.winfo_width()
            if panel_width <= 1:
                panel_width = 280  # 使用新的面板宽度

            # 根据面板宽度动态调整字体大小 - 整体增大字体
            if panel_width > 250:
                font_size = 10  # 从7增加到10
            elif panel_width > 200:
                font_size = 9   # 从6增加到9
            else:
                font_size = 8   # 从5增加到8

            # 确保字体不会太小
            font_size = max(font_size, 8)  # 最小字体从5增加到8
        except:
            font_size = 9  # 默认字体大小从6增加到9

        # 绑定面板大小变化事件（只绑定一次）
        if not hasattr(self, 'metrics_resize_bound'):
            self.time_metrics_frame.bind('<Configure>', self.on_metrics_panel_resize)
            self.freq_metrics_frame.bind('<Configure>', self.on_metrics_panel_resize)
            self.fault_metrics_frame.bind('<Configure>', self.on_metrics_panel_resize)
            self.metrics_resize_bound = True

        # 显示时域指标 - 根据空间选择显示模式，优先使用标准模式
        if font_size <= 7:  # 调整阈值，只有在字体很小时才使用紧凑模式
            # 超紧凑模式：每行显示两个指标
            metrics_list = list(time_metrics.items())
            for i in range(0, len(metrics_list), 2):
                row = i // 2

                # 第一个指标
                name1, value1 = metrics_list[i]
                metric1_text = f"{name1}:{value1}"
                label1 = Label(self.time_metrics_frame, text=metric1_text,
                             font=("微软雅黑", font_size),
                             fg=self.colors['success'],
                             bg=self.colors['surface'],
                             anchor='w')
                label1.grid(row=row, column=0, sticky='w', padx=2, pady=1)  # 增加间距

                # 第二个指标（如果存在）
                if i + 1 < len(metrics_list):
                    name2, value2 = metrics_list[i + 1]
                    metric2_text = f"{name2}:{value2}"
                    label2 = Label(self.time_metrics_frame, text=metric2_text,
                                 font=("微软雅黑", font_size),
                                 fg=self.colors['success'],
                                 bg=self.colors['surface'],
                                 anchor='w')
                    label2.grid(row=row, column=1, sticky='w', padx=2, pady=1)  # 增加间距
        else:
            # 标准模式：每行显示一个指标
            row = 0
            for name, value in time_metrics.items():
                # 指标名称
                name_label = Label(self.time_metrics_frame, text=f"{name}:",
                                 font=("微软雅黑", font_size, "bold"),
                                 fg=self.colors['text_primary'],
                                 bg=self.colors['surface'],
                                 anchor='w')
                name_label.grid(row=row, column=0, sticky='w', padx=(3, 2), pady=1)  # 增加间距

                # 指标值
                value_label = Label(self.time_metrics_frame, text=value,
                                  font=("微软雅黑", font_size),
                                  fg=self.colors['success'],
                                  bg=self.colors['surface'],
                                  anchor='e')
                value_label.grid(row=row, column=1, sticky='e', padx=(2, 3), pady=1)  # 增加间距
                row += 1

        # 配置时域指标网格权重
        self.time_metrics_frame.grid_columnconfigure(0, weight=1)
        self.time_metrics_frame.grid_columnconfigure(1, weight=1)

        # 显示频域指标 - 根据空间选择显示模式，优先使用标准模式
        if font_size <= 7:  # 调整阈值，只有在字体很小时才使用紧凑模式
            # 超紧凑模式：每行显示两个指标
            metrics_list = list(freq_metrics.items())
            for i in range(0, len(metrics_list), 2):
                row = i // 2

                # 第一个指标
                name1, value1 = metrics_list[i]
                metric1_text = f"{name1}:{value1}"
                label1 = Label(self.freq_metrics_frame, text=metric1_text,
                             font=("微软雅黑", font_size),
                             fg=self.colors['success'],
                             bg=self.colors['surface'],
                             anchor='w')
                label1.grid(row=row, column=0, sticky='w', padx=2, pady=1)  # 增加间距

                # 第二个指标（如果存在）
                if i + 1 < len(metrics_list):
                    name2, value2 = metrics_list[i + 1]
                    metric2_text = f"{name2}:{value2}"
                    label2 = Label(self.freq_metrics_frame, text=metric2_text,
                                 font=("微软雅黑", font_size),
                                 fg=self.colors['success'],
                                 bg=self.colors['surface'],
                                 anchor='w')
                    label2.grid(row=row, column=1, sticky='w', padx=2, pady=1)  # 增加间距
        else:
            # 标准模式：每行显示一个指标
            row = 0
            for name, value in freq_metrics.items():
                # 指标名称
                name_label = Label(self.freq_metrics_frame, text=f"{name}:",
                                 font=("微软雅黑", font_size, "bold"),
                                 fg=self.colors['text_primary'],
                                 bg=self.colors['surface'],
                                 anchor='w')
                name_label.grid(row=row, column=0, sticky='w', padx=(3, 2), pady=1)  # 增加间距

                # 指标值
                value_label = Label(self.freq_metrics_frame, text=value,
                                  font=("微软雅黑", font_size),
                                  fg=self.colors['success'],
                                  bg=self.colors['surface'],
                                  anchor='e')
                value_label.grid(row=row, column=1, sticky='e', padx=(2, 3), pady=1)  # 增加间距
                row += 1

        # 配置频域指标网格权重
        self.freq_metrics_frame.grid_columnconfigure(0, weight=1)
        self.freq_metrics_frame.grid_columnconfigure(1, weight=1)

        # 显示故障指标 - 根据空间选择显示模式，优先使用标准模式
        if font_size <= 7:  # 调整阈值，只有在字体很小时才使用紧凑模式
            # 超紧凑模式：每行显示两个指标
            metrics_list = list(fault_metrics.items())
            for i in range(0, len(metrics_list), 2):
                row = i // 2

                # 第一个指标
                name1, value1 = metrics_list[i]
                metric1_text = f"{name1}:{value1}"
                label1 = Label(self.fault_metrics_frame, text=metric1_text,
                             font=("微软雅黑", font_size),
                             fg=self.colors['warning'],
                             bg=self.colors['surface'],
                             anchor='w')
                label1.grid(row=row, column=0, sticky='w', padx=2, pady=1)  # 增加间距

                # 第二个指标（如果存在）
                if i + 1 < len(metrics_list):
                    name2, value2 = metrics_list[i + 1]
                    metric2_text = f"{name2}:{value2}"
                    label2 = Label(self.fault_metrics_frame, text=metric2_text,
                                 font=("微软雅黑", font_size),
                                 fg=self.colors['warning'],
                                 bg=self.colors['surface'],
                                 anchor='w')
                    label2.grid(row=row, column=1, sticky='w', padx=2, pady=1)  # 增加间距
        else:
            # 标准模式：每行显示一个指标
            row = 0
            for name, value in fault_metrics.items():
                # 指标名称
                name_label = Label(self.fault_metrics_frame, text=f"{name}:",
                                 font=("微软雅黑", font_size, "bold"),
                                 fg=self.colors['text_primary'],
                                 bg=self.colors['surface'],
                                 anchor='w')
                name_label.grid(row=row, column=0, sticky='w', padx=(3, 2), pady=1)  # 增加间距

                # 指标值
                value_label = Label(self.fault_metrics_frame, text=value,
                                  font=("微软雅黑", font_size),
                                  fg=self.colors['warning'],
                                  bg=self.colors['surface'],
                                  anchor='e')
                value_label.grid(row=row, column=1, sticky='e', padx=(2, 3), pady=1)  # 增加间距
                row += 1

        # 配置故障指标网格权重
        self.fault_metrics_frame.grid_columnconfigure(0, weight=1)
        self.fault_metrics_frame.grid_columnconfigure(1, weight=1)





    def on_attachment_selected(self, event=None):
        """当选择附件时的回调"""
        selected_name = self.attachment_var.get()
        if not selected_name:
            return

        # 查找对应的附件
        for idx, att in enumerate(self.attachments):
            if isinstance(att['content'], dict) and "visual_data" in att['content']:
                display_name = att['name'][:15] + '...' if len(att['name']) > 15 else att['name']
                if display_name == selected_name:
                    self.select_for_visualization(att, idx)
                    break

    def update_attachment_combo(self):
        """更新附件选择下拉框"""
        # 获取所有可视化的附件
        viz_attachments = []
        for att in self.attachments:
            if isinstance(att['content'], dict) and "visual_data" in att['content']:
                display_name = att['name'][:15] + '...' if len(att['name']) > 15 else att['name']
                viz_attachments.append(display_name)

        # 更新下拉框
        self.attachment_combo['values'] = viz_attachments

        # 如果有附件但没有选中任何一个，选中第一个
        if viz_attachments and not hasattr(self, 'selected_attachment_index'):
            self.attachment_combo.set(viz_attachments[0])
            self.on_attachment_selected()

    def show_visualization_placeholder(self):
        """显示可视化占位符"""
        # 清空现有内容
        for widget in self.viz_content_frame.winfo_children():
            widget.destroy()

        # 创建占位符
        placeholder_frame = Frame(self.viz_content_frame, bg='white', relief='solid', bd=1)
        placeholder_frame.pack(fill=BOTH, expand=True)

        # 轴承图案作为占位符
        canvas = Canvas(placeholder_frame, bg='white', highlightthickness=0)
        canvas.pack(fill=BOTH, expand=True)

        def draw_placeholder_bearing(event=None):
            canvas.delete("all")
            width = canvas.winfo_width()
            height = canvas.winfo_height()

            if width > 1 and height > 1:
                center_x, center_y = width // 2, height // 2
                radius = min(width, height) // 4

                # 绘制轴承示意图
                # 外圈
                canvas.create_oval(center_x - radius, center_y - radius,
                                 center_x + radius, center_y + radius,
                                 outline=self.colors['border'], width=3)

                # 内圈
                inner_radius = radius * 0.6
                canvas.create_oval(center_x - inner_radius, center_y - inner_radius,
                                 center_x + inner_radius, center_y + inner_radius,
                                 outline=self.colors['border'], width=2)

                # 滚动体
                import math
                ball_radius = radius * 0.08
                ball_orbit_radius = radius * 0.8
                for i in range(8):
                    angle = i * 2 * math.pi / 8
                    ball_x = center_x + ball_orbit_radius * math.cos(angle)
                    ball_y = center_y + ball_orbit_radius * math.sin(angle)

                    canvas.create_oval(ball_x - ball_radius, ball_y - ball_radius,
                                     ball_x + ball_radius, ball_y + ball_radius,
                                     outline=self.colors['border'], width=1,
                                     fill=self.colors['background'])

                # 提示文字
                canvas.create_text(center_x, center_y + radius + 30,
                                 text="请添加数据文件进行可视化分析",
                                 font=("微软雅黑", 12),
                                 fill=self.colors['text_secondary'])

        canvas.bind('<Configure>', draw_placeholder_bearing)
        canvas.after(100, draw_placeholder_bearing)

    def show_visualization_type(self, viz_type):
        """显示指定类型的可视化"""
        self.current_viz_type = viz_type

        # 如果有选中的附件，显示该附件的可视化
        if hasattr(self, 'selected_attachment') and self.selected_attachment:
            self.show_attachment_visualization(self.selected_attachment, viz_type)
            return

        # 如果没有选中附件但有数据，显示第一个附件的可视化
        if self.attachments:
            for attachment in self.attachments:
                if isinstance(attachment['content'], dict) and "visual_data" in attachment['content']:
                    self.show_attachment_visualization(attachment, viz_type)
                    return

        # 如果没有数据，显示提示
        self.show_no_data_message(viz_type)

    def show_no_data_message(self, viz_type):
        """显示无数据提示"""
        for widget in self.viz_content_frame.winfo_children():
            widget.destroy()

        message_frame = Frame(self.viz_content_frame, bg='white')
        message_frame.pack(fill=BOTH, expand=True)

        viz_names = {'time': '时域分析', 'freq': '频域分析', 'wavelet': '小波分析'}

        Label(message_frame, text=f"📊 {viz_names.get(viz_type, '数据可视化')}",
              font=("微软雅黑", 16, "bold"),
              fg=self.colors['text_primary'], bg='white').pack(pady=50)

        Label(message_frame, text="请先添加包含振动数据的文件\n支持 .mat 和 .xlsx 格式",
              font=("微软雅黑", 12),
              fg=self.colors['text_secondary'], bg='white').pack(pady=20)

    def show_attachment_visualization(self, attachment, viz_type):
        """在右侧面板显示附件的可视化"""
        if not isinstance(attachment['content'], dict) or "visual_data" not in attachment['content']:
            return

        # 清空现有内容
        for widget in self.viz_content_frame.winfo_children():
            widget.destroy()

        data_dict = attachment['content']['visual_data']
        signal = data_dict["time_signal"]
        fs = data_dict["fs"]
        freq_axis = data_dict["freq_axis"]
        env_spectrum = data_dict["envelope_spectrum"]

        # 统一的单图显示逻辑 - 进一步增大图像尺寸
        fig = Figure(figsize=(8, 5.2), dpi=85, facecolor=self.colors['surface_light'])
        ax = fig.add_subplot(111, facecolor=self.colors['surface'])

        if viz_type == 'time':
            # 时域波形 - 科技感样式
            t = np.arange(len(signal)) / fs
            ax.plot(t, signal, color=self.colors['accent'], linewidth=1.2, alpha=0.9)
            ax.set_title('时域分析', fontsize=12, color=self.colors['text_primary'],
                        fontfamily='Microsoft YaHei', weight='bold')
            ax.set_xlabel('时间 (s)', fontsize=10, color=self.colors['text_secondary'], fontfamily='Microsoft YaHei')
            ax.set_ylabel('幅值', fontsize=10, color=self.colors['text_secondary'], fontfamily='Microsoft YaHei')
            ax.grid(True, alpha=0.2, color=self.colors['border'])

            # 设置科技感样式
            for spine in ax.spines.values():
                spine.set_color(self.colors['border'])
            ax.tick_params(colors=self.colors['text_secondary'], labelsize=8)

        elif viz_type == 'freq':
            # 频域包络谱 - 自适应尺度调整
            ax.plot(freq_axis, env_spectrum, color=self.colors['success'], linewidth=1.2, alpha=0.9)

            # 设置中文标题和标签
            ax.set_title("包络谱", fontsize=12, color=self.colors['text_primary'],
                        fontfamily='Microsoft YaHei', weight='bold')
            ax.set_xlabel("频率 (Hz)", fontsize=10, color=self.colors['text_secondary'],
                         fontfamily='Microsoft YaHei')
            ax.set_ylabel("振动幅值", fontsize=10, color=self.colors['text_secondary'],
                         fontfamily='Microsoft YaHei')

            # 包络谱完整显示 - 不进行自适应调节
            # 显示完整的频率范围和幅值范围
            ax.set_xlim(0, freq_axis[-1])  # 显示完整频率范围
            ax.set_ylim(0, np.max(env_spectrum) * 1.05)  # 显示完整幅值范围，留5%余量

            # 设置科技感样式
            ax.grid(True, alpha=0.2, color=self.colors['border'])
            for spine in ax.spines.values():
                spine.set_color(self.colors['border'])
            ax.tick_params(colors=self.colors['text_secondary'], labelsize=8)

        elif viz_type == 'wavelet':
            # 小波变换时频图 - 只显示小波变换结果
            t = np.arange(len(signal)) / fs

            # 计算连续小波变换
            default_wavelet = 'morl'
            default_level = 4
            coef, scales = self.perform_wavelet_transform(signal, wavelet=default_wavelet, level=default_level, fs=fs)

            # 创建小波时频图
            im = ax.imshow(np.abs(coef), extent=[t[0], t[-1], scales[-1], scales[0]],
                           aspect='auto', cmap='jet')

            # 设置标题和标签 - 减小字体
            ax.set_title(f"连续小波变换时频图 ({default_wavelet}, level={default_level})",
                        fontsize=10, color=self.colors['text_primary'],
                        fontfamily='Microsoft YaHei', weight='bold')
            ax.set_xlabel("时间 (s)", fontsize=8, color=self.colors['text_secondary'],
                         fontfamily='Microsoft YaHei')
            ax.set_ylabel("频率尺度", fontsize=8, color=self.colors['text_secondary'],
                         fontfamily='Microsoft YaHei')
            ax.tick_params(labelsize=7)  # 减小刻度标签字体

            # 添加颜色条
            cbar = fig.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label('振动幅值', fontsize=9, fontfamily='Microsoft YaHei')
            cbar.ax.tick_params(labelsize=8, colors=self.colors['text_secondary'])

            # 设置科技感样式
            for spine in ax.spines.values():
                spine.set_color(self.colors['border'])
            ax.tick_params(colors=self.colors['text_secondary'], labelsize=8)


        # 调整布局参数，最大化图像显示区域，并为y轴标题预留空间
        fig.tight_layout(pad=1.2, h_pad=0.8, w_pad=0.8, rect=[0.08, 0, 1, 1])  # 左侧预留8%空间给y轴标题

        # 嵌入到tkinter中
        canvas = FigureCanvasTkAgg(fig, self.viz_content_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=BOTH, expand=True, padx=3, pady=3)

        # 添加图像缩放功能
        self.setup_image_zoom(canvas, ax, fig)

        # 添加工具栏 - 进一步减小工具栏占用空间
        toolbar_frame = Frame(self.viz_content_frame, bg='white', height=22)
        toolbar_frame.pack(fill=X, pady=(1, 0))
        toolbar_frame.pack_propagate(False)

        toolbar = NavigationToolbar2Tk(canvas, toolbar_frame)
        toolbar.update()

        # 隐藏工具栏中不必要的按钮，只保留基本功能
        for child in toolbar.winfo_children():
            if hasattr(child, 'config'):
                try:
                    child.config(font=("微软雅黑", 7))
                except:
                    pass

    def setup_image_zoom(self, canvas, ax, fig):
        """设置图像缩放功能 - Ctrl+滚轮缩放"""
        # 存储原始视图范围
        self.original_xlim = ax.get_xlim()
        self.original_ylim = ax.get_ylim()

        def on_scroll(event):
            """处理滚轮事件"""
            print(f"滚轮事件触发: button={event.button}, key={event.key}, inaxes={event.inaxes is not None}")

            # 获取当前鼠标位置
            if event.inaxes != ax:
                print("鼠标不在图像区域内")
                return

            # 获取当前视图范围
            cur_xlim = ax.get_xlim()
            cur_ylim = ax.get_ylim()

            # 获取鼠标在数据坐标系中的位置
            xdata = event.xdata
            ydata = event.ydata

            if xdata is None or ydata is None:
                print("无法获取鼠标数据坐标")
                return

            # 设置缩放因子
            if event.button == 'up':
                scale_factor = 0.9  # 放大
                print("放大图像")
            elif event.button == 'down':
                scale_factor = 1.1  # 缩小
                print("缩小图像")
            else:
                print(f"未知滚轮方向: {event.button}")
                return

            # 计算新的视图范围，以鼠标位置为中心
            new_width = (cur_xlim[1] - cur_xlim[0]) * scale_factor
            new_height = (cur_ylim[1] - cur_ylim[0]) * scale_factor

            relx = (cur_xlim[1] - xdata) / (cur_xlim[1] - cur_xlim[0])
            rely = (cur_ylim[1] - ydata) / (cur_ylim[1] - cur_ylim[0])

            new_xlim = [xdata - new_width * (1 - relx), xdata + new_width * relx]
            new_ylim = [ydata - new_height * (1 - rely), ydata + new_height * rely]

            print(f"缩放前: xlim={cur_xlim}, ylim={cur_ylim}")
            print(f"缩放后: xlim={new_xlim}, ylim={new_ylim}")

            # 应用新的视图范围
            ax.set_xlim(new_xlim)
            ax.set_ylim(new_ylim)

            # 重绘图形
            canvas.draw()
            print("图像重绘完成")

        def on_key_press(event):
            """处理按键事件"""
            if event.key == 'r':
                # 按R键重置视图
                ax.set_xlim(self.original_xlim)
                ax.set_ylim(self.original_ylim)
                canvas.draw()

        # 连接matplotlib事件
        canvas.mpl_connect('scroll_event', on_scroll)
        canvas.mpl_connect('key_press_event', on_key_press)

        # 简化的tkinter滚轮事件处理
        def simple_zoom(event):
            print(f"简化滚轮事件: delta={getattr(event, 'delta', 'unknown')}")

            # 获取当前视图范围
            cur_xlim = ax.get_xlim()
            cur_ylim = ax.get_ylim()

            # 计算视图中心点
            center_x = (cur_xlim[0] + cur_xlim[1]) / 2
            center_y = (cur_ylim[0] + cur_ylim[1]) / 2

            # 设置缩放因子
            if hasattr(event, 'delta'):
                if event.delta > 0:
                    scale_factor = 0.9  # 放大
                    print("放大图像")
                else:
                    scale_factor = 1.1  # 缩小
                    print("缩小图像")
            else:
                # 对于Button-4/Button-5事件
                scale_factor = 0.9  # 默认放大
                print("滚轮事件（默认放大）")

            # 计算新的视图范围
            width = cur_xlim[1] - cur_xlim[0]
            height = cur_ylim[1] - cur_ylim[0]

            new_width = width * scale_factor
            new_height = height * scale_factor

            new_xlim = [center_x - new_width/2, center_x + new_width/2]
            new_ylim = [center_y - new_height/2, center_y + new_height/2]

            print(f"缩放: {cur_xlim} -> {new_xlim}")

            # 应用新的视图范围
            ax.set_xlim(new_xlim)
            ax.set_ylim(new_ylim)

            # 重绘图形
            canvas.draw()
            print("图像重绘完成")

        # 绑定简化的滚轮事件
        canvas.get_tk_widget().bind("<MouseWheel>", simple_zoom)
        canvas.get_tk_widget().bind("<Button-4>", simple_zoom)
        canvas.get_tk_widget().bind("<Button-5>", simple_zoom)

        # 设置焦点，使键盘事件生效
        canvas.get_tk_widget().focus_set()

        # 添加提示信息
        self.show_zoom_hint()

    def on_metrics_panel_resize(self, event=None):
        """当指标面板大小改变时更新字体大小"""
        if hasattr(self, 'current_time_metrics') and hasattr(self, 'current_freq_metrics') and hasattr(self, 'current_fault_metrics'):
            # 延迟更新，避免频繁重绘
            if hasattr(self, 'resize_timer'):
                self.master.after_cancel(self.resize_timer)
            self.resize_timer = self.master.after(200, self.update_metrics_font_size)

    def update_metrics_font_size(self):
        """更新指标显示的字体大小"""
        if hasattr(self, 'current_time_metrics') and hasattr(self, 'current_freq_metrics') and hasattr(self, 'current_fault_metrics'):
            self.update_metrics_display(self.current_time_metrics, self.current_freq_metrics, self.current_fault_metrics)



    def show_zoom_hint(self):
        """显示缩放操作提示"""
        if hasattr(self, 'zoom_hint_label'):
            self.zoom_hint_label.destroy()

        hint_frame = Frame(self.viz_content_frame, bg=self.colors['surface_light'])
        hint_frame.pack(fill=X, pady=(1, 0))

        self.zoom_hint_label = Label(hint_frame,
                                   text="💡 提示：滚轮缩放图像，R键重置视图",
                                   font=("微软雅黑", 7),
                                   fg=self.colors['text_secondary'],
                                   bg=self.colors['surface_light'])
        self.zoom_hint_label.pack(pady=1)

    def setup_multi_axis_zoom(self, canvas, axes_list, fig):
        """为多个子图设置缩放功能"""
        # 存储每个轴的原始视图范围
        self.original_limits = {}
        for i, ax in enumerate(axes_list):
            self.original_limits[i] = {
                'xlim': ax.get_xlim(),
                'ylim': ax.get_ylim()
            }

        def on_scroll(event):
            """处理滚轮事件"""
            # 检查是否按下Ctrl键
            if event.key != 'ctrl':
                return

            # 找到鼠标所在的子图
            target_ax = None
            ax_index = None
            for i, ax in enumerate(axes_list):
                if event.inaxes == ax:
                    target_ax = ax
                    ax_index = i
                    break

            if target_ax is None:
                return

            # 获取当前视图范围
            cur_xlim = target_ax.get_xlim()
            cur_ylim = target_ax.get_ylim()

            # 获取鼠标在数据坐标系中的位置
            xdata = event.xdata
            ydata = event.ydata

            if xdata is None or ydata is None:
                return

            # 设置缩放因子
            if event.button == 'up':
                scale_factor = 0.9  # 放大
            elif event.button == 'down':
                scale_factor = 1.1  # 缩小
            else:
                return

            # 计算新的视图范围，以鼠标位置为中心
            new_width = (cur_xlim[1] - cur_xlim[0]) * scale_factor
            new_height = (cur_ylim[1] - cur_ylim[0]) * scale_factor

            relx = (cur_xlim[1] - xdata) / (cur_xlim[1] - cur_xlim[0])
            rely = (cur_ylim[1] - ydata) / (cur_ylim[1] - cur_ylim[0])

            new_xlim = [xdata - new_width * (1 - relx), xdata + new_width * relx]
            new_ylim = [ydata - new_height * (1 - rely), ydata + new_height * rely]

            # 应用新的视图范围
            target_ax.set_xlim(new_xlim)
            target_ax.set_ylim(new_ylim)

            # 重绘图形
            canvas.draw()

        def on_key_press(event):
            """处理按键事件"""
            if event.key == 'r':
                # 按R键重置所有子图的视图
                for i, ax in enumerate(axes_list):
                    if i in self.original_limits:
                        ax.set_xlim(self.original_limits[i]['xlim'])
                        ax.set_ylim(self.original_limits[i]['ylim'])
                canvas.draw()

        # 连接事件
        canvas.mpl_connect('scroll_event', on_scroll)
        canvas.mpl_connect('key_press_event', on_key_press)

        # 设置焦点，使键盘事件生效
        canvas.get_tk_widget().focus_set()

    def update_visualization_on_file_add(self):
        """当添加文件时更新可视化"""
        # 更新附件选择下拉框
        self.update_attachment_combo()

        if hasattr(self, 'current_viz_type') and self.current_viz_type:
            self.show_visualization_type(self.current_viz_type)
        elif self.attachments:
            # 如果有数据文件，默认显示时域分析
            for attachment in self.attachments:
                if isinstance(attachment['content'], dict) and "visual_data" in attachment['content']:
                    self.show_visualization_type('time')
                    break

    def setup_enterprise_style(self):
        """设置企业级样式"""
        style = ttk.Style()

        # 配置进度条样式
        style.configure("Custom.Horizontal.TProgressbar",
                       background=self.colors['primary'],
                       troughcolor=self.colors['border'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])

        # 配置按钮样式
        style.configure("Primary.TButton",
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none')

        style.configure("Secondary.TButton",
                       background=self.colors['secondary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none')

        style.configure("Success.TButton",
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none')

    def create_title_bar(self, parent):
        """创建标题栏"""
        title_frame = Frame(parent, bg=self.colors['primary'], height=50)
        title_frame.pack(fill=X, pady=(0, 10))
        title_frame.pack_propagate(False)

        # 左侧标题
        left_frame = Frame(title_frame, bg=self.colors['primary'])
        left_frame.pack(side=LEFT, fill=Y, padx=20)

        Label(left_frame, text="智能故障诊断系统",
              font=("微软雅黑", 14, "bold"),
              fg='white', bg=self.colors['primary']).pack(anchor=W, pady=12)

        # 右侧状态指示器
        right_frame = Frame(title_frame, bg=self.colors['primary'])
        right_frame.pack(side=RIGHT, fill=Y, padx=20)

        self.connection_status = Label(right_frame, text="● 系统就绪",
                                     font=("微软雅黑", 10),
                                     fg=self.colors['success'], bg=self.colors['primary'])
        self.connection_status.pack(anchor=E, pady=15)

    def create_status_bar(self):
        """创建企业级状态栏"""
        status_frame = Frame(self.master, bg=self.colors['border'], height=30)
        status_frame.pack(side=BOTTOM, fill=X)
        status_frame.pack_propagate(False)

        # 状态文本
        self.status = StringVar()
        self.status.set("系统就绪")

        status_label = Label(status_frame, textvariable=self.status,
                           font=("微软雅黑", 9),
                           fg=self.colors['text_primary'],
                           bg=self.colors['surface'],
                           anchor=W, padx=10)
        status_label.pack(side=LEFT, fill=BOTH, expand=True)

        # 版本信息
        version_label = Label(status_frame, text="v1.0 ",
                            font=("微软雅黑", 8),
                            fg=self.colors['text_secondary'],
                            bg=self.colors['surface'],
                            anchor=E, padx=10)
        version_label.pack(side=RIGHT)

    def create_menu(self):
        """创建企业级菜单栏"""
        menubar = Menu(self.master, bg=self.colors['surface'], fg=self.colors['text_primary'],
                      activebackground=self.colors['primary'], activeforeground='white',
                      font=('微软雅黑', 9))
        self.master.config(menu=menubar)

        # 文件菜单
        file_menu = Menu(menubar, tearoff=0, bg=self.colors['surface'], fg=self.colors['text_primary'],
                        activebackground=self.colors['primary'], activeforeground='white',
                        font=('微软雅黑', 9))
        menubar.add_cascade(label="📁 文件", menu=file_menu)
        file_menu.add_command(label="📎 添加文件", command=self.upload_files)
        file_menu.add_command(label="🗑️ 清空附件", command=self.clear_all_attachments)
        file_menu.add_separator()
        file_menu.add_command(label="💾 保存对话", command=self.save_conversation)
        file_menu.add_separator()
        file_menu.add_command(label="🚪 退出系统", command=self.master.quit)

        # 分析菜单
        analysis_menu = Menu(menubar, tearoff=0, bg=self.colors['surface'], fg=self.colors['text_primary'],
                           activebackground=self.colors['primary'], activeforeground='white',
                           font=('微软雅黑', 9))
        menubar.add_cascade(label="📊 分析", menu=analysis_menu)

        analysis_menu.add_command(label="⚙️ 轴承参数", command=lambda: self.show_bearing_params_dialog())
        analysis_menu.add_command(label="🌪️ 风机参数", command=lambda: self.show_wind_turbine_params_dialog())
        analysis_menu.add_separator()
        analysis_menu.add_command(label="📋 导出特征", command=self.export_features)
        analysis_menu.add_separator()
        analysis_menu.add_command(label="🧠 深度学习诊断", command=self.show_deep_learning_window)

        # 设置菜单
        settings_menu = Menu(menubar, tearoff=0, bg=self.colors['surface'], fg=self.colors['text_primary'],
                           activebackground=self.colors['primary'], activeforeground='white',
                           font=('微软雅黑', 9))
        menubar.add_cascade(label="⚙️ 设置", menu=settings_menu)
        settings_menu.add_command(label="🔑 API配置", command=self.show_settings)
        settings_menu.add_command(label="🔗 检查连接", command=self.check_api_connection)
        settings_menu.add_separator()
        settings_menu.add_command(label="📊 系统状态", command=self.show_system_status)



    def setup_attachment_ui(self, parent):
        """设置企业级附件管理界面"""
        # 主框架
        frame = Frame(parent, bg='white', relief='solid', bd=1)
        frame.pack(fill=X, pady=(0, 10))

        # 标题栏
        title_frame = Frame(frame, bg=self.colors['surface'], height=35)
        title_frame.pack(fill=X)
        title_frame.pack_propagate(False)

        Label(title_frame, text="📁 文件管理",
              font=("微软雅黑", 13, "bold"),
              fg=self.colors['text_primary'], bg=self.colors['surface']).pack(side=LEFT, padx=15, pady=8)

        # 文件计数标签
        self.file_count_label = Label(title_frame, text="(0 个文件)",
                                     font=("微软雅黑", 11),
                                     fg=self.colors['text_secondary'], bg=self.colors['surface'])
        self.file_count_label.pack(side=LEFT, pady=8)

        # 附件容器
        container = Frame(frame, bg='white')
        container.pack(fill=X, expand=True, padx=10, pady=5)

        self.canvas = Canvas(container, height=60, bg=self.colors['background'],
                           highlightthickness=0, relief='flat')
        h_scroll = ttk.Scrollbar(container, orient="horizontal", command=self.canvas.xview)
        h_scroll.pack(side=BOTTOM, fill=X)
        self.canvas.pack(side=TOP, fill=X, expand=True)
        self.canvas.configure(xscrollcommand=h_scroll.set)

        self.attachment_container = Frame(self.canvas, bg=self.colors['background'])
        self.canvas.create_window((0, 0), window=self.attachment_container, anchor="nw")

        # 按钮区域 - 减小间距
        btn_frame = Frame(frame, bg='white')
        btn_frame.pack(fill=X, padx=10, pady=(0, 6))

        # 主要操作按钮 - 再缩小30%
        Button(btn_frame, text="📎 添加文件",
               font=("微软雅黑", 8, "bold"),
               bg=self.colors['primary'], fg='white',
               relief='flat', padx=7, pady=3,
               command=self.upload_files).pack(side=LEFT, padx=(0, 2))

        Button(btn_frame, text="🗑️ 清空全部",
               font=("微软雅黑", 8),
               bg=self.colors['error'], fg='white',
               relief='flat', padx=6, pady=3,
               command=self.clear_all_attachments).pack(side=LEFT, padx=2)



    def setup_input_ui(self, parent):
        """设置企业级输入区域界面"""
        # 主框架
        frame = Frame(parent, bg='white', relief='solid', bd=1)
        frame.pack(fill=X, pady=(0, 10))

        # 标题栏
        title_frame = Frame(frame, bg=self.colors['surface'], height=35)
        title_frame.pack(fill=X)
        title_frame.pack_propagate(False)

        Label(title_frame, text="💬 问题输入",
              font=("微软雅黑", 13, "bold"),
              fg=self.colors['text_primary'], bg=self.colors['surface']).pack(side=LEFT, padx=15, pady=8)

        Label(title_frame, text="支持 Ctrl+Enter 快速发送",
              font=("微软雅黑", 10),
              fg=self.colors['text_secondary'], bg=self.colors['surface']).pack(side=RIGHT, padx=15, pady=8)

        # 输入区域
        input_container = Frame(frame, bg='white')
        input_container.pack(fill=BOTH, expand=True, padx=10, pady=5)

        self.input_text = scrolledtext.ScrolledText(input_container, height=3, wrap=WORD,
                                                   font=('微软雅黑', 12),
                                                   bg='white', fg='#2d3748',
                                                   relief='solid', bd=1,
                                                   selectbackground=self.colors['secondary'],
                                                   insertbackground=self.colors['primary'],
                                                   padx=10, pady=8)
        self.input_text.pack(fill=BOTH, expand=True)
        self.input_text.bind("<Control-Return>", self.send_question)

        # 按钮区域 - 减小间距
        btn_frame = Frame(frame, bg='white')
        btn_frame.pack(fill=X, padx=10, pady=(0, 6))

        # 右侧按钮组
        right_buttons = Frame(btn_frame, bg='white')
        right_buttons.pack(side=RIGHT)

        self.send_button = Button(right_buttons, text="🚀 发送内容",
                                 font=("微软雅黑", 8, "bold"),
                                 bg=self.colors['primary'], fg='white',
                                 relief='flat', padx=8, pady=3,
                                 command=self.send_question)
        self.send_button.pack(side=RIGHT, padx=(2, 0))

        self.clear_input_button = Button(right_buttons, text="🗑️ 清空",
                                        font=("微软雅黑", 8),
                                        bg=self.colors['border'], fg='white',
                                        relief='flat', padx=7, pady=3,
                                        command=lambda: self.input_text.delete("1.0", END))
        self.clear_input_button.pack(side=RIGHT, padx=(0, 2))

    def setup_history_ui(self, parent):
        """设置企业级对话历史界面"""
        # 主框架
        frame = Frame(parent, bg='white', relief='solid', bd=1)
        frame.pack(fill=BOTH, expand=True)

        # 标题栏
        title_frame = Frame(frame, bg=self.colors['surface'], height=35)
        title_frame.pack(fill=X)
        title_frame.pack_propagate(False)

        Label(title_frame, text="🤖 诊断对话",
              font=("微软雅黑", 13, "bold"),
              fg=self.colors['text_primary'], bg=self.colors['surface']).pack(side=LEFT, padx=15, pady=8)

        # 右侧按钮组
        button_frame = Frame(title_frame, bg=self.colors['surface'])
        button_frame.pack(side=RIGHT, padx=8, pady=6)

        # 清空历史按钮
        Button(button_frame, text="🗑️ 清空历史",
               font=("微软雅黑", 8),
               bg=self.colors['warning'], fg='white',
               relief='flat', padx=6, pady=2,
               command=self.clear_history).pack(side=RIGHT, padx=(5, 0))

        # 停止回答按钮
        self.stop_button = Button(button_frame, text="⏹️ 停止回答",
                                 font=("微软雅黑", 8),
                                 bg=self.colors['error'], fg='white',
                                 relief='flat', padx=6, pady=2,
                                 command=self.stop_answer,
                                 state='disabled')
        self.stop_button.pack(side=RIGHT, padx=(0, 5))

        # API参数设置按钮
        Button(button_frame, text="⚙️ API设置",
               font=("微软雅黑", 8),
               bg=self.colors['primary'], fg='white',
               relief='flat', padx=6, pady=2,
               command=self.show_api_settings).pack(side=RIGHT, padx=(0, 5))

        # 对话区域
        history_container = Frame(frame, bg='white')
        history_container.pack(fill=BOTH, expand=True, padx=10, pady=(5, 10))

        self.history_text = scrolledtext.ScrolledText(history_container, wrap=WORD, state='disabled',
                                                     font=('微软雅黑', 12),
                                                     bg='white',
                                                     fg='#2d3748',
                                                     relief='solid', bd=1,
                                                     padx=15, pady=15,
                                                     selectbackground=self.colors['secondary'])
        self.history_text.pack(fill=BOTH, expand=True)

        # 为不同角色设置清晰的颜色样式
        self.history_text.tag_config("role_red", foreground='#e53e3e',
                                     font=('微软雅黑', 14, 'bold'))
        self.history_text.tag_config("role_user", foreground='#2b6cb0',
                                     font=('微软雅黑', 13, 'bold'),
                                     spacing1=12, spacing3=6)
        self.history_text.tag_config("role_answer", foreground='#38a169',
                                     font=('微软雅黑', 13, 'bold'),
                                     spacing1=12, spacing3=6)
        self.history_text.tag_config("role_reasoning", foreground='#d69e2e',
                                     font=('微软雅黑', 13, 'bold'),
                                     spacing1=12, spacing3=6)

        # 添加内容样式 - 增大字体和间距
        self.history_text.tag_config("content", font=('微软雅黑', 11),
                                     spacing1=3, spacing3=10,
                                     lmargin1=25, lmargin2=25)

    def stop_answer(self):
        """停止AI回答生成"""
        self.stop_generation = True
        self.stop_button.config(state='disabled')

        # 在对话历史中添加停止提示
        self.append_history("\n\n[用户停止了回答生成]\n\n")

        # 记录到日志
        self.log_message("系统", "用户停止了回答生成")

        # 更新状态
        self.set_status("回答已停止")

    def show_api_settings(self):
        """显示API参数设置窗口"""
        # 创建设置窗口
        settings_window = Toplevel(self.master)
        settings_window.title("DeepSeek API 参数设置")
        settings_window.geometry("600x900")  # 进一步增加高度
        settings_window.configure(bg='white')
        settings_window.resizable(False, False)

        # 设置窗口图标和位置
        try:
            settings_window.iconbitmap("icon.ico")
        except:
            pass

        # 居中显示
        settings_window.transient(self.master)
        settings_window.grab_set()

        # 主标题
        title_frame = Frame(settings_window, bg=self.colors['primary'], height=50)
        title_frame.pack(fill=X)
        title_frame.pack_propagate(False)

        Label(title_frame, text="⚙️ DeepSeek API 参数设置",
              font=("微软雅黑", 14, "bold"),
              fg='white', bg=self.colors['primary']).pack(expand=True)

        # 创建滚动区域
        main_frame = Frame(settings_window, bg='white')
        main_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)

        # 参数设置区域
        params_frame = LabelFrame(main_frame, text="📊 参数配置",
                                 font=("微软雅黑", 11, "bold"),
                                 bg='white', fg='#2c3e50')
        params_frame.pack(fill=X, pady=(0, 10))

        # 存储参数变量
        param_vars = {}

        # 参数定义和说明
        param_info = {
            'temperature': {
                'label': 'Temperature (创造性)',
                'range': '0.0 - 2.0',
                'tooltip': '控制输出的随机性和创造性\n• 0.0-0.3: 非常保守，确定性强\n• 0.3-0.5: 平衡准确性和多样性 (推荐)\n• 0.5-0.8: 较有创造性\n• 0.8+: 高创造性，可能不够准确\n\n故障诊断建议值: 0.4'
            },
            'top_p': {
                'label': 'Top P (核采样)',
                'range': '0.0 - 1.0',
                'tooltip': '控制候选词汇的范围\n• 0.1-0.5: 非常保守的词汇选择\n• 0.6-0.9: 平衡的词汇多样性 (推荐)\n• 0.9+: 高词汇多样性\n\n故障诊断建议值: 0.85'
            },
            'max_tokens': {
                'label': 'Max Tokens (最大长度)',
                'range': '1 - 32000',
                'tooltip': '限制单次回答的最大长度\n• 4000-6000: 简洁回答\n• 6000-8000: 详细分析 (推荐)\n• 8000+: 非常详细的报告\n\n故障诊断建议值: 8000'
            },
            'frequency_penalty': {
                'label': 'Frequency Penalty (频率惩罚)',
                'range': '-2.0 - 2.0',
                'tooltip': '减少重复词汇的使用\n• 0.0: 无惩罚\n• 0.1-0.5: 轻微减少重复 (推荐)\n• 0.5+: 强烈避免重复\n\n故障诊断建议值: 0.3'
            },
            'presence_penalty': {
                'label': 'Presence Penalty (存在惩罚)',
                'range': '-2.0 - 2.0',
                'tooltip': '鼓励讨论新话题和概念\n• 0.0: 无惩罚\n• 0.1-0.3: 轻微鼓励新内容 (推荐)\n• 0.3+: 强烈鼓励新话题\n\n故障诊断建议值: 0.2'
            }
        }

        # 创建参数输入控件
        for i, (param_key, info) in enumerate(param_info.items()):
            param_frame = Frame(params_frame, bg='white')
            param_frame.pack(fill=X, padx=12, pady=5)

            # 标签和问号提示
            label_frame = Frame(param_frame, bg='white')
            label_frame.pack(fill=X)

            Label(label_frame, text=info['label'],
                  font=("微软雅黑", 10, "bold"),
                  bg='white', fg='#2c3e50').pack(side=LEFT)

            # 问号提示按钮
            help_btn = Label(label_frame, text="❓",
                           font=("微软雅黑", 10),
                           bg='white', fg=self.colors['primary'],
                           cursor='hand2')
            help_btn.pack(side=LEFT, padx=(5, 0))

            # 创建工具提示
            self.create_tooltip(help_btn, info['tooltip'])

            # 范围说明
            Label(param_frame, text=f"范围: {info['range']}",
                  font=("微软雅黑", 8),
                  bg='white', fg='#7f8c8d').pack(anchor=W)

            # 输入框 - 根据参数类型选择变量类型
            if param_key == 'max_tokens':
                param_var = IntVar(value=int(self.api_params[param_key]))
            else:
                param_var = DoubleVar(value=self.api_params[param_key])
            param_vars[param_key] = param_var

            entry = Entry(param_frame, textvariable=param_var,
                         font=("微软雅黑", 10), width=20,
                         relief='solid', bd=1)
            entry.pack(anchor=W, pady=(2, 0))

        # 场景预设区域
        presets_frame = LabelFrame(main_frame, text="🎯 场景预设",
                                  font=("微软雅黑", 11, "bold"),
                                  bg='white', fg='#2c3e50')
        presets_frame.pack(fill=X, pady=(0, 10))

        # 预设配置
        presets = {
            '快速诊断': {
                'desc': '追求速度和简洁',
                'params': {'temperature': 0.3, 'top_p': 0.8, 'max_tokens': 4000, 'frequency_penalty': 0.3, 'presence_penalty': 0.2}
            },
            '详细分析': {
                'desc': '追求全面和深入',
                'params': {'temperature': 0.4, 'top_p': 0.9, 'max_tokens': 10000, 'frequency_penalty': 0.3, 'presence_penalty': 0.2}
            },
            '保守诊断': {
                'desc': '追求最高准确性',
                'params': {'temperature': 0.2, 'top_p': 0.7, 'max_tokens': 6000, 'frequency_penalty': 0.2, 'presence_penalty': 0.1}
            },
            '默认设置': {
                'desc': '平衡各项指标 (推荐)',
                'params': {'temperature': 0.4, 'top_p': 0.85, 'max_tokens': 8000, 'frequency_penalty': 0.3, 'presence_penalty': 0.2}
            }
        }

        def apply_preset(preset_params):
            for param_key, value in preset_params.items():
                if param_key in param_vars:
                    param_vars[param_key].set(value)

        # 创建预设按钮
        preset_buttons_frame = Frame(presets_frame, bg='white')
        preset_buttons_frame.pack(fill=X, padx=12, pady=8)

        for i, (preset_name, preset_data) in enumerate(presets.items()):
            btn_frame = Frame(preset_buttons_frame, bg='white')
            btn_frame.pack(fill=X, pady=2)

            btn = Button(btn_frame, text=f"📋 {preset_name}",
                        font=("微软雅黑", 9),
                        bg=self.colors['accent'], fg='white',
                        relief='flat', padx=10, pady=3,
                        command=lambda p=preset_data['params']: apply_preset(p))
            btn.pack(side=LEFT)

            Label(btn_frame, text=preset_data['desc'],
                  font=("微软雅黑", 8),
                  bg='white', fg='#7f8c8d').pack(side=LEFT, padx=(10, 0))

        # 按钮区域 - 确保有足够空间显示
        button_frame = Frame(main_frame, bg='white', height=60)
        button_frame.pack(fill=X, pady=(15, 10))
        button_frame.pack_propagate(False)  # 防止按钮区域被压缩

        def save_settings():
            try:
                # 验证并保存参数
                validated_params = {}
                for param_key, var in param_vars.items():
                    try:
                        value = var.get()

                        # 数据类型验证和转换
                        if param_key == 'max_tokens':
                            # 确保max_tokens是整数
                            if isinstance(value, float):
                                value = int(value)
                            elif isinstance(value, str):
                                value = int(float(value))  # 先转float再转int，处理"8000.0"这种情况
                            else:
                                value = int(value)
                        else:
                            # 其他参数转为浮点数
                            value = float(value)

                        # 范围验证
                        if param_key == 'temperature' and not (0.0 <= value <= 2.0):
                            raise ValueError(f"Temperature 必须在 0.0-2.0 范围内，当前值: {value}")
                        elif param_key == 'top_p' and not (0.0 <= value <= 1.0):
                            raise ValueError(f"Top P 必须在 0.0-1.0 范围内，当前值: {value}")
                        elif param_key == 'max_tokens' and not (1 <= value <= 32000):
                            raise ValueError(f"Max Tokens 必须在 1-32000 范围内，当前值: {value}")
                        elif param_key in ['frequency_penalty', 'presence_penalty'] and not (-2.0 <= value <= 2.0):
                            raise ValueError(f"{param_key} 必须在 -2.0-2.0 范围内，当前值: {value}")

                        validated_params[param_key] = value

                    except (ValueError, TypeError) as e:
                        if "invalid literal" in str(e) or "could not convert" in str(e):
                            raise ValueError(f"参数 {param_key} 的值格式不正确，请输入有效数字")
                        else:
                            raise e

                # 更新API参数
                self.api_params.update(validated_params)

                # 保存到配置文件
                if self.save_api_config():
                    messagebox.showinfo("保存成功",
                        f"✅ API参数设置已保存！\n\n"
                        f"Temperature: {validated_params['temperature']}\n"
                        f"Top P: {validated_params['top_p']}\n"
                        f"Max Tokens: {validated_params['max_tokens']}\n"
                        f"Frequency Penalty: {validated_params['frequency_penalty']}\n"
                        f"Presence Penalty: {validated_params['presence_penalty']}\n\n"
                        f"设置将在下次对话时生效。")
                    settings_window.destroy()
                else:
                    messagebox.showerror("保存失败",
                        "❌ 参数设置保存到文件失败！\n\n"
                        "可能原因：\n"
                        "• 文件权限不足\n"
                        "• 磁盘空间不足\n"
                        "• 文件被其他程序占用\n\n"
                        "当前会话中的设置已生效，但重启后会丢失。")

            except ValueError as e:
                messagebox.showerror("参数错误", f"❌ 参数验证失败！\n\n{str(e)}\n\n请检查输入的数值是否正确。")
            except Exception as e:
                error_details = f"错误类型: {type(e).__name__}\n错误信息: {str(e)}"
                messagebox.showerror("保存错误", f"❌ 保存设置时发生未知错误！\n\n{error_details}\n\n请重试或联系技术支持。")

        def reset_defaults():
            apply_preset(presets['默认设置']['params'])

        # 保存和重置按钮
        Button(button_frame, text="💾 保存设置",
               font=("微软雅黑", 10, "bold"),
               bg=self.colors['success'], fg='white',
               relief='flat', padx=20, pady=8,
               command=save_settings).pack(side=RIGHT, padx=(5, 0))

        Button(button_frame, text="🔄 重置默认",
               font=("微软雅黑", 10),
               bg=self.colors['warning'], fg='white',
               relief='flat', padx=20, pady=8,
               command=reset_defaults).pack(side=RIGHT, padx=(5, 0))

        Button(button_frame, text="❌ 取消",
               font=("微软雅黑", 10),
               bg=self.colors['text_secondary'], fg='white',
               relief='flat', padx=20, pady=8,
               command=settings_window.destroy).pack(side=RIGHT)

    def create_tooltip(self, widget, text):
        """创建工具提示"""
        def on_enter(event):
            tooltip = Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.configure(bg='#ffffe0', relief='solid', bd=1)

            label = Label(tooltip, text=text,
                         font=("微软雅黑", 9),
                         bg='#ffffe0', fg='black',
                         justify=LEFT, wraplength=300)
            label.pack()

            # 定位工具提示
            x = widget.winfo_rootx() + 25
            y = widget.winfo_rooty() + 25
            tooltip.geometry(f"+{x}+{y}")

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)

    def clear_history(self):
        """清空对话历史"""
        if messagebox.askyesno("确认", "确定要清空对话历史吗？"):
            # 记录清空历史的操作到日志
            self.log_message("系统", "用户清空了对话历史")

            # 清空UI中的历史
            self.history_text.configure(state='normal')
            self.history_text.delete("1.0", END)
            self.history_text.configure(state='disabled')

            # 保留系统提示词，重置对话上下文
            system_msg = self.messages[0]
            self.messages = [system_msg]

            # 更新状态
            self.status.set("对话历史已清空")

            # 在日志中添加分隔线，表示新的对话开始
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n{'='*50}\n[{timestamp}] 新对话开始\n{'='*50}\n\n")
            except Exception as e:
                print(f"写入日志分隔线失败: {str(e)}")

    def post_init_check(self):
        """初始化后的检查"""
        self.update_splash_progress(80, "检查系统组件...")
        try:
            if 'chi_sim' not in pytesseract.get_languages(config=''):
                messagebox.showwarning("语言包缺失", "中文语言包未安装，可能影响识别效果")
        except Exception as e:
            self.show_error(f"初始化检查失败: {str(e)}")



    def show_system_status(self):
        """显示系统状态窗口"""
        status_window = Toplevel(self.master)
        status_window.title("系统状态")
        status_window.geometry("500x400")
        status_window.transient(self.master)
        status_window.grab_set()
        status_window.configure(bg=self.colors['background'])

        # 标题
        title_frame = Frame(status_window, bg=self.colors['primary'], height=50)
        title_frame.pack(fill=X)
        title_frame.pack_propagate(False)

        Label(title_frame, text="📊 系统状态监控",
              font=("微软雅黑", 14, "bold"),
              fg='white', bg=self.colors['primary']).pack(pady=15)

        # 状态信息
        content_frame = Frame(status_window, bg=self.colors['background'])
        content_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # API状态
        api_frame = Frame(content_frame, bg='white', relief='solid', bd=1)
        api_frame.pack(fill=X, pady=(0, 10))

        Label(api_frame, text="🔗 API连接状态",
              font=("微软雅黑", 11, "bold"),
              fg=self.colors['text_primary'], bg='white').pack(anchor=W, padx=10, pady=5)

        api_status = "连接正常" if hasattr(self, 'key') and self.key else "未配置"
        Label(api_frame, text=f"状态: {api_status}",
              font=("微软雅黑", 10),
              fg=self.colors['success'] if api_status == "连接正常" else self.colors['error'],
              bg='white').pack(anchor=W, padx=20, pady=2)

        # 文件状态
        file_frame = Frame(content_frame, bg='white', relief='solid', bd=1)
        file_frame.pack(fill=X, pady=(0, 10))

        Label(file_frame, text="📁 文件管理状态",
              font=("微软雅黑", 11, "bold"),
              fg=self.colors['text_primary'], bg='white').pack(anchor=W, padx=10, pady=5)

        Label(file_frame, text=f"已加载文件: {len(self.attachments)} 个",
              font=("微软雅黑", 10),
              fg=self.colors['text_primary'], bg='white').pack(anchor=W, padx=20, pady=2)

        # 系统信息
        sys_frame = Frame(content_frame, bg='white', relief='solid', bd=1)
        sys_frame.pack(fill=X, pady=(0, 10))

        Label(sys_frame, text="💻 系统信息",
              font=("微软雅黑", 11, "bold"),
              fg=self.colors['text_primary'], bg='white').pack(anchor=W, padx=10, pady=5)

        import platform
        Label(sys_frame, text=f"操作系统: {platform.system()} {platform.release()}",
              font=("微软雅黑", 10),
              fg=self.colors['text_primary'], bg='white').pack(anchor=W, padx=20, pady=2)

        Label(sys_frame, text=f"Python版本: {platform.python_version()}",
              font=("微软雅黑", 10),
              fg=self.colors['text_primary'], bg='white').pack(anchor=W, padx=20, pady=2)



    def save_conversation(self):
        """保存当前对话到文件"""
        # 创建一个带时间戳的默认文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"对话记录_{timestamp}.txt"

        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialfile=default_filename,
            initialdir=self.work_dir,  # 默认保存到deepseek助手文件夹
            title="保存对话"
        )

        if not file_path:
            return

        try:
            # 从历史文本框中获取对话文本
            self.history_text.configure(state='normal')
            conversation_text = self.history_text.get("1.0", END)
            self.history_text.configure(state='disabled')

            # 保存对话内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"# DeepSeek故障诊断助手对话记录 - 导出于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(conversation_text)

            # 同时确保日志文件已更新
            self.log_message("系统", f"对话已导出到文件: {os.path.basename(file_path)}")

            # 更新状态
            self.status.set(f"对话已保存至 {os.path.basename(file_path)}")

            # 显示成功消息
            messagebox.showinfo("保存成功", f"对话已成功保存至:\n{file_path}")

        except Exception as e:
            error_msg = f"保存对话失败: {str(e)}"
            self.show_error(error_msg)
            self.log_message("系统错误", error_msg)

    def export_features(self):
        """导出特征数据"""
        if not os.path.exists(self.mat_feature_log_file):
            messagebox.showinfo("导出特征", "暂无特征数据可导出")
            return
        file_path = filedialog.asksaveasfilename(defaultextension=".txt",
                                                 filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                                                 initialfile="特征数据导出.txt",
                                                 initialdir=self.work_dir,  # 默认保存到deepseek助手文件夹
                                                 title="导出特征数据")
        if not file_path:
            return
        try:
            with open(self.mat_feature_log_file, 'r', encoding='utf-8') as src:
                content = src.read()
            with open(file_path, 'w', encoding='utf-8') as dst:
                dst.write(content)
            self.status.set(f"特征数据已导出至 {os.path.basename(file_path)}")
        except Exception as e:
            self.show_error(f"导出特征失败: {str(e)}")

    # ============== 附件管理 ==============
    def clear_all_attachments(self):
        """清空所有附件"""
        if self.attachments and messagebox.askyesno("确认", "确定要清空所有附件吗？"):
            self.attachments.clear()
            self.update_attachment_list()
            self.status.set("已清空所有附件")

    def upload_files(self):
        """上传文件"""
        file_types = [
            ('支持的文件', '*.txt *.docx *.pdf *.mat *.xlsx *.xls *.png *.jpg *.jpeg'),
            ('文本文件', '*.txt'),
            ('Word文档', '*.docx'),
            ('PDF文件', '*.pdf'),
            ('MAT文件', '*.mat'),
            ('Excel文件', '*.xlsx *.xls'),
            ('图像文件', '*.png *.jpg *.jpeg'),
            ('所有文件', '*.*')
        ]
        try:
            if files := filedialog.askopenfilenames(filetypes=file_types):
                progress_window = Toplevel(self.master)
                progress_window.title("文件处理中")
                progress_window.geometry("300x100")
                progress_window.transient(self.master)
                Label(progress_window, text="正在处理文件...").pack(pady=10)
                progress = ttk.Progressbar(progress_window, mode="determinate", length=250)
                progress.pack(pady=10)
                progress["maximum"] = len(files)
                self.master.update()
                for i, path in enumerate(files):
                    self.add_attachment(path)
                    progress["value"] = i + 1
                    progress_window.title(f"处理文件 ({i + 1}/{len(files)})")
                    self.master.update()
                progress_window.destroy()
                self.update_attachment_list()
                self.status.set(f"已添加 {len(files)} 个文件")
        except Exception as e:
            self.show_error(f"上传失败: {str(e)}")

    def add_attachment(self, file_path):
        """添加附件"""
        try:
            lower_path = file_path.lower()
            is_image = lower_path.endswith(('.png', '.jpg', '.jpeg'))
            if is_image:
                content = self.process_image(file_path)
            elif lower_path.endswith('.pdf'):
                content = self.process_pdf(file_path)
            elif lower_path.endswith('.mat'):
                content = self.process_mat_file(file_path)
            elif lower_path.endswith(('.xlsx', '.xls')):
                content = self.process_excel_file(file_path)
            else:
                content = self.process_file(file_path)
            for att in self.attachments:
                if att['path'] == file_path:
                    if messagebox.askyesno("文件已存在", f"文件 {os.path.basename(file_path)} 已存在，是否替换？"):
                        self.attachments.remove(att)
                    else:
                        return
            self.attachments.append({
                'path': file_path,
                'name': os.path.basename(file_path),
                'content': content,
                'is_image': is_image,
                'ocr_text': self.get_ocr_text(file_path) if is_image else ""
            })
        except Exception as e:
            self.show_error(f"文件 {os.path.basename(file_path)} 处理失败: {str(e)}")

    def update_attachment_list(self):
        """更新企业级附件列表显示"""
        for widget in self.attachment_container.winfo_children():
            widget.destroy()

        # 更新文件计数
        file_count = len(self.attachments)
        self.file_count_label.config(text=f"({file_count} 个文件)")

        for idx, att in enumerate(self.attachments):
            # 创建紧凑的文件卡片，如果是选中的附件则高亮显示
            is_selected = hasattr(self, 'selected_attachment_index') and self.selected_attachment_index == idx
            card_bg = self.colors['secondary'] if is_selected else 'white'
            card_frame = Frame(self.attachment_container, bg=card_bg, relief='solid',
                             bd=2 if is_selected else 1)
            card_frame.pack(side=LEFT, padx=2, pady=2)

            # 文件图标和名称（更紧凑）
            info_frame = Frame(card_frame, bg=card_bg)
            info_frame.pack(fill=X, padx=4, pady=2)

            # 根据文件类型选择图标
            file_ext = os.path.splitext(att['name'])[1].lower()
            if file_ext in ['.xlsx', '.xls']:
                icon = "📊"
            elif file_ext == '.mat':
                icon = "🔬"
            elif file_ext == '.pdf':
                icon = "📄"
            elif file_ext in ['.png', '.jpg', '.jpeg']:
                icon = "🖼️"
            else:
                icon = "📁"

            # 文件信息（更简洁）
            file_name = att['name'][:12] + '...' if len(att['name']) > 12 else att['name']
            text_color = 'white' if is_selected else self.colors['text_primary']
            info_bg = card_bg
            Label(info_frame, text=f"{icon} {file_name}",
                  font=("微软雅黑", 8),
                  fg=text_color, bg=info_bg).pack(anchor=W)

            # 按钮区域（更紧凑）
            btn_frame = Frame(card_frame, bg=card_bg)
            btn_frame.pack(fill=X, padx=2, pady=(0, 2))

            # 预览按钮
            Button(btn_frame, text="👁️",
                   font=("微软雅黑", 7),
                   bg=self.colors['secondary'], fg='white',
                   relief='flat', width=2, pady=1,
                   command=lambda a=att: self.show_preview(a)).pack(side=LEFT, padx=1)

            # 可视化按钮（仅对数据文件显示）
            if isinstance(att['content'], dict) and "visual_data" in att['content']:
                Button(btn_frame, text="📈",
                       font=("微软雅黑", 7),
                       bg=self.colors['success'], fg='white',
                       relief='flat', width=2, pady=1,
                       command=lambda a=att, i=idx: self.select_for_visualization(a, i)).pack(side=LEFT, padx=1)

            # 删除按钮
            Button(btn_frame, text="❌",
                   font=("微软雅黑", 7),
                   bg=self.colors['error'], fg='white',
                   relief='flat', width=2, pady=1,
                   command=lambda i=idx: self.remove_attachment(i)).pack(side=LEFT, padx=1)

        self.attachment_container.update_idletasks()
        self.canvas.config(scrollregion=self.canvas.bbox("all"))

        # 更新右侧可视化
        self.update_visualization_on_file_add()

    def setup_data_acquisition_panel(self, parent):
        """设置数据采集控制面板"""
        # 数据采集控制面板 - 限制高度
        daq_frame = Frame(parent, bg=self.colors['surface'], relief='flat', bd=0, height=300)
        daq_frame.pack(fill=X, pady=(10, 0))
        daq_frame.pack_propagate(False)  # 防止子组件改变框架大小

        # 标题栏
        daq_title_frame = Frame(daq_frame, bg=self.colors['primary'], height=40)
        daq_title_frame.pack(fill=X)
        daq_title_frame.pack_propagate(False)

        Label(daq_title_frame, text="📡 数据采集控制",
              font=("微软雅黑", 12, "bold"),
              fg='white', bg=self.colors['primary']).pack(side=LEFT, padx=15, pady=10)

        # 状态指示区域
        status_container = Frame(daq_title_frame, bg=self.colors['primary'])
        status_container.pack(side=RIGHT, padx=15, pady=10)

        # 采集状态
        self.daq_status_label = Label(status_container, textvariable=self.daq_status_var,
                                     font=("微软雅黑", 10),
                                     fg=self.colors['accent_bright'], bg=self.colors['primary'])
        self.daq_status_label.pack(side=LEFT, padx=(0, 10))

        # DIO状态显示
        dio_frame = Frame(status_container, bg=self.colors['primary'])
        dio_frame.pack(side=LEFT)

        Label(dio_frame, text="DIO1:",
              font=("微软雅黑", 9),
              fg='white', bg=self.colors['primary']).pack(side=LEFT)

        self.dio_status_label = Label(dio_frame, textvariable=self.dio_status_var,
                                     font=("微软雅黑", 9, "bold"),
                                     fg=self.colors['success'], bg=self.colors['primary'])
        self.dio_status_label.pack(side=LEFT, padx=(2, 0))

        # 控制面板内容 - 三列分布
        daq_content = Frame(daq_frame, bg='white')
        daq_content.pack(fill=BOTH, expand=True, padx=8, pady=5)

        # 第一列：采集参数（占35%，增加宽度）
        params_frame = Frame(daq_content, bg='white', width=300)
        params_frame.pack(side=LEFT, fill=Y, padx=(0, 5))
        params_frame.pack_propagate(False)

        # 第二列：采集控制（占30%）
        control_frame = Frame(daq_content, bg='white', width=250)
        control_frame.pack(side=LEFT, fill=Y, padx=(0, 5))
        control_frame.pack_propagate(False)

        # 第三列：实时时域信号（占35%，减少宽度）
        realtime_frame = Frame(daq_content, bg='white')
        realtime_frame.pack(side=LEFT, fill=BOTH, expand=True)

        # === 第一列：参数设置区域 ===
        params_label = Label(params_frame, text="📊 采集参数",
                           font=("微软雅黑", 11, "bold"),
                           fg='#2c3e50', bg='white')
        params_label.pack(anchor=W, pady=(0, 3))

        # 参数容器 - 优化滚动性能
        params_container = Frame(params_frame, bg='white')
        params_container.pack(fill=BOTH, expand=True, pady=(0, 3))

        # 创建滚动画布和滚动条
        params_canvas = Canvas(params_container, bg='white', height=250, highlightthickness=0)
        params_scrollbar = ttk.Scrollbar(params_container, orient="vertical", command=params_canvas.yview)
        params_scrollable_frame = Frame(params_canvas, bg='white')

        # 优化滚动区域更新
        def update_scroll_region(event=None):
            params_canvas.configure(scrollregion=params_canvas.bbox("all"))
            # 确保滚动条在内容超出时显示
            canvas_height = params_canvas.winfo_height()
            content_height = params_scrollable_frame.winfo_reqheight()
            if content_height > canvas_height:
                params_scrollbar.pack(side="right", fill="y")
            else:
                params_scrollbar.pack_forget()

        params_scrollable_frame.bind("<Configure>", update_scroll_region)
        params_canvas.bind("<Configure>", update_scroll_region)

        # 创建滚动窗口
        canvas_window = params_canvas.create_window((0, 0), window=params_scrollable_frame, anchor="nw")
        params_canvas.configure(yscrollcommand=params_scrollbar.set)

        # 优化鼠标滚轮支持 - 减少卡顿
        def on_mousewheel(event):
            # 检查是否需要滚动
            if params_canvas.canvasy(0) != params_canvas.canvasy(params_canvas.winfo_height()):
                # 使用更平滑的滚动
                params_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            return "break"

        # 绑定滚轮事件到多个组件
        def bind_mousewheel(widget):
            widget.bind("<MouseWheel>", on_mousewheel)
            widget.bind("<Button-4>", lambda e: on_mousewheel(type('obj', (object,), {'delta': 120})()))
            widget.bind("<Button-5>", lambda e: on_mousewheel(type('obj', (object,), {'delta': -120})()))

        bind_mousewheel(params_canvas)
        bind_mousewheel(params_scrollable_frame)

        # 确保画布宽度自适应
        def configure_canvas_width(event):
            canvas_width = event.width
            params_canvas.itemconfig(canvas_window, width=canvas_width)

        params_canvas.bind('<Configure>', configure_canvas_width)

        # 布局滚动组件
        params_canvas.pack(side="left", fill="both", expand=True)
        params_scrollbar.pack(side="right", fill="y")

        # 强制更新滚动区域 - 确保滚动条正确显示
        def force_scroll_update():
            params_canvas.update_idletasks()
            update_scroll_region()
            # 如果内容超出显示区域，确保滚动条可见
            if params_scrollable_frame.winfo_reqheight() > 250:
                params_scrollbar.pack(side="right", fill="y")

        # 延迟执行强制更新，确保所有组件都已创建
        params_canvas.after(100, force_scroll_update)

        # 绑定滚轮事件到所有子组件的函数
        def bind_mousewheel_to_children(parent):
            """递归绑定鼠标滚轮事件到所有子组件"""
            bind_mousewheel(parent)
            for child in parent.winfo_children():
                bind_mousewheel_to_children(child)

        # 延迟绑定滚轮事件，确保所有组件都已创建
        def delayed_bind_mousewheel():
            bind_mousewheel_to_children(params_scrollable_frame)

        params_canvas.after(200, delayed_bind_mousewheel)

        # === 硬件配置参数 ===
        hardware_label = Label(params_scrollable_frame, text="🔧 硬件配置",
                              font=("微软雅黑", 8, "bold"),
                              fg='#2c3e50', bg='white')
        hardware_label.pack(anchor=W, pady=(0, 3))

        # 第一行：板卡号和物理通道（并列显示）
        hw_row1 = Frame(params_scrollable_frame, bg='white')
        hw_row1.pack(fill=X, pady=2)

        # 左侧：板卡号
        board_frame = Frame(hw_row1, bg='white')
        board_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))

        Label(board_frame, text="板卡号:", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        board_num_entry = Entry(board_frame, textvariable=self.daq_board_num_var,
                               font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        board_num_entry.pack(anchor=W, pady=(1, 0))

        # 右侧：物理通道
        channel_frame = Frame(hw_row1, bg='white')
        channel_frame.pack(side=LEFT, fill=X, expand=True)

        Label(channel_frame, text="物理通道:", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        channel_entry = Entry(channel_frame, textvariable=self.daq_channel_var,
                             font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        channel_entry.pack(anchor=W, pady=(1, 0))

        # === 采集参数 ===
        acq_label = Label(params_scrollable_frame, text="📊 采集参数",
                         font=("微软雅黑", 8, "bold"),
                         fg='#2c3e50', bg='white')
        acq_label.pack(anchor=W, pady=(8, 3))

        # 第二行：采样频率和采样点数（并列显示）
        row1 = Frame(params_scrollable_frame, bg='white')
        row1.pack(fill=X, pady=2)

        # 左侧：采样频率
        freq_frame = Frame(row1, bg='white')
        freq_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))

        Label(freq_frame, text="采样频率(Hz):", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        sample_rate_entry = Entry(freq_frame, textvariable=self.daq_sample_rate_var,
                                 font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        sample_rate_entry.pack(anchor=W, pady=(1, 0))
        sample_rate_entry.bind('<KeyRelease>', lambda e: self.update_filename_preview())

        # 右侧：采样点数
        points_frame = Frame(row1, bg='white')
        points_frame.pack(side=LEFT, fill=X, expand=True)

        Label(points_frame, text="采样点数:", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        num_points_entry = Entry(points_frame, textvariable=self.daq_num_points_var,
                                font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        num_points_entry.pack(anchor=W, pady=(1, 0))

        # 第二行：设备ID（单独一行）
        row2 = Frame(params_scrollable_frame, bg='white')
        row2.pack(fill=X, pady=2)

        Label(row2, text="设备ID:", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        device_id_entry = Entry(row2, textvariable=self.daq_device_id_var,
                               font=("微软雅黑", 8), width=30, relief='solid', bd=1)
        device_id_entry.pack(anchor=W, pady=(1, 0))

        # === 轴承参数（用于Excel命名） ===
        bearing_label = Label(params_scrollable_frame, text="🔧 轴承参数",
                             font=("微软雅黑", 8, "bold"),
                             fg='#2c3e50', bg='white')
        bearing_label.pack(anchor=W, pady=(8, 3))

        # 第三行：轴承ID和型号（并列显示）
        row3 = Frame(params_scrollable_frame, bg='white')
        row3.pack(fill=X, pady=2)

        # 左侧：轴承ID
        bearing_id_frame = Frame(row3, bg='white')
        bearing_id_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))

        Label(bearing_id_frame, text="轴承ID:", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        bearing_id_entry = Entry(bearing_id_frame, textvariable=self.daq_bearing_id_var,
                                font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        bearing_id_entry.pack(anchor=W, pady=(1, 0))
        bearing_id_entry.bind('<KeyRelease>', lambda e: self.update_filename_preview())

        # 右侧：轴承型号
        bearing_model_frame = Frame(row3, bg='white')
        bearing_model_frame.pack(side=LEFT, fill=X, expand=True)

        Label(bearing_model_frame, text="轴承型号:", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        bearing_model_entry = Entry(bearing_model_frame, textvariable=self.daq_bearing_model_var,
                                   font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        bearing_model_entry.pack(anchor=W, pady=(1, 0))
        bearing_model_entry.bind('<KeyRelease>', lambda e: self.update_filename_preview())

        # 第四行：转速和滚动体数量（并列显示）
        row4 = Frame(params_scrollable_frame, bg='white')
        row4.pack(fill=X, pady=2)

        # 左侧：转速
        rpm_frame = Frame(row4, bg='white')
        rpm_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))

        Label(rpm_frame, text="转速(rpm):", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        rpm_entry = Entry(rpm_frame, textvariable=self.daq_rpm_var,
                         font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        rpm_entry.pack(anchor=W, pady=(1, 0))
        rpm_entry.bind('<KeyRelease>', lambda e: self.update_filename_preview())

        # 右侧：滚动体数量
        ball_count_frame = Frame(row4, bg='white')
        ball_count_frame.pack(side=LEFT, fill=X, expand=True)

        Label(ball_count_frame, text="滚动体数:", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        ball_count_entry = Entry(ball_count_frame, textvariable=self.daq_ball_count_var,
                                font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        ball_count_entry.pack(anchor=W, pady=(1, 0))
        ball_count_entry.bind('<KeyRelease>', lambda e: self.update_filename_preview())

        # 第五行：滚动体直径和节径直径（并列显示）
        row5 = Frame(params_scrollable_frame, bg='white')
        row5.pack(fill=X, pady=2)

        # 左侧：滚动体直径
        ball_diameter_frame = Frame(row5, bg='white')
        ball_diameter_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 5))

        Label(ball_diameter_frame, text="滚动体直径(mm):", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        ball_diameter_entry = Entry(ball_diameter_frame, textvariable=self.daq_ball_diameter_var,
                                   font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        ball_diameter_entry.pack(anchor=W, pady=(1, 0))
        ball_diameter_entry.bind('<KeyRelease>', lambda e: self.update_filename_preview())

        # 右侧：节径直径
        pitch_diameter_frame = Frame(row5, bg='white')
        pitch_diameter_frame.pack(side=LEFT, fill=X, expand=True)

        Label(pitch_diameter_frame, text="节径直径(mm):", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        pitch_diameter_entry = Entry(pitch_diameter_frame, textvariable=self.daq_pitch_diameter_var,
                                    font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        pitch_diameter_entry.pack(anchor=W, pady=(1, 0))
        pitch_diameter_entry.bind('<KeyRelease>', lambda e: self.update_filename_preview())

        # 第六行：接触角（单独一行）
        row6 = Frame(params_scrollable_frame, bg='white')
        row6.pack(fill=X, pady=2)

        Label(row6, text="接触角(度):", font=("微软雅黑", 7, "bold"),
              fg='#2c3e50', bg='white').pack(anchor=W)

        contact_angle_entry = Entry(row6, textvariable=self.daq_contact_angle_var,
                                   font=("微软雅黑", 8), width=15, relief='solid', bd=1)
        contact_angle_entry.pack(anchor=W, pady=(1, 0))
        contact_angle_entry.bind('<KeyRelease>', lambda e: self.update_filename_preview())

        # === 文件名预览 ===
        preview_label = Label(params_scrollable_frame, text="📄 文件名预览",
                             font=("微软雅黑", 8, "bold"),
                             fg='#2c3e50', bg='white')
        preview_label.pack(anchor=W, pady=(8, 3))

        # 文件名预览显示
        preview_frame = Frame(params_scrollable_frame, bg=self.colors['surface_light'], relief='solid', bd=1)
        preview_frame.pack(fill=X, pady=2)

        self.filename_preview_label = Label(preview_frame, textvariable=self.daq_filename_preview_var,
                                           font=("微软雅黑", 7), wraplength=280,
                                           fg=self.colors['text_primary'], bg=self.colors['surface_light'],
                                           justify=LEFT, anchor=W)
        self.filename_preview_label.pack(padx=5, pady=3, fill=X)

        # === 第二列：数据采集控制 ===
        control_label = Label(control_frame, text="🎛️ 采集控制",
                             font=("微软雅黑", 11, "bold"),
                             fg='#2c3e50', bg='white')
        control_label.pack(anchor=W, pady=(0, 5))

        # 控制按钮区域
        control_buttons_frame = Frame(control_frame, bg='white')
        control_buttons_frame.pack(fill=X, pady=(0, 5))

        # 开始/停止按钮
        self.daq_start_button = Button(control_buttons_frame, text="🚀 开始采集",
                                      font=("微软雅黑", 9, "bold"),
                                      bg=self.colors['success'], fg='white',
                                      relief='flat', padx=10, pady=3,
                                      command=self.start_data_acquisition)
        self.daq_start_button.pack(side=LEFT, padx=(0, 5))

        self.daq_stop_button = Button(control_buttons_frame, text="⏹️ 停止采集",
                                     font=("微软雅黑", 9),
                                     bg=self.colors['error'], fg='white',
                                     relief='flat', padx=10, pady=3,
                                     command=self.stop_data_acquisition,
                                     state='disabled')
        self.daq_stop_button.pack(side=LEFT, padx=(0, 5))

        # 第二行按钮
        control_buttons_frame2 = Frame(control_frame, bg='white')
        control_buttons_frame2.pack(fill=X, pady=(0, 5))

        # 保存数据按钮
        Button(control_buttons_frame2, text="💾 保存数据",
               font=("微软雅黑", 9),
               bg=self.colors['primary'], fg='white',
               relief='flat', padx=10, pady=3,
               command=self.save_daq_data).pack(side=LEFT, padx=(0, 5))

        # 预览按钮
        Button(control_buttons_frame2, text="📊 预览Excel",
               font=("微软雅黑", 9),
               bg=self.colors['accent'], fg='white',
               relief='flat', padx=10, pady=3,
               command=self.preview_daq_excel).pack(side=LEFT)

        # 第三行按钮
        control_buttons_frame3 = Frame(control_frame, bg='white')
        control_buttons_frame3.pack(fill=X, pady=(0, 10))

        # 测试连接按钮
        Button(control_buttons_frame3, text="🔧 测试连接",
               font=("微软雅黑", 9),
               bg=self.colors['warning'], fg='white',
               relief='flat', padx=10, pady=3,
               command=self.test_daq_connection).pack(side=LEFT)

        # === 第三列：时域波形显示 ===
        realtime_label = Label(realtime_frame, text="📈 时域波形",
                              font=("微软雅黑", 11, "bold"),
                              fg='#2c3e50', bg='white')
        realtime_label.pack(anchor=W, pady=(0, 5))

        # 实时显示画布区域 - 限制高度
        realtime_display_frame = Frame(realtime_frame, bg='white', relief='solid', bd=1, height=200)
        realtime_display_frame.pack(fill=BOTH, expand=True, pady=(0, 5))
        realtime_display_frame.pack_propagate(False)

        # 创建matplotlib图形
        self.setup_realtime_plot(realtime_display_frame)

        # 状态信息区域 - 减少高度
        status_frame = Frame(realtime_frame, bg=self.colors['background'], height=30)
        status_frame.pack(fill=X, pady=(3, 0))
        status_frame.pack_propagate(False)

        self.daq_info_label = Label(status_frame,
                                   text="等待开始数据采集... ⚠️ 请确保DAQ硬件已正确连接",
                                   font=("微软雅黑", 8),
                                   fg=self.colors['text_secondary'],
                                   bg=self.colors['background'],
                                   justify=CENTER)
        self.daq_info_label.pack(expand=True)

    def setup_realtime_plot(self, parent):
        """设置实时绘图区域"""
        try:
            # 创建matplotlib图形 - 调整尺寸适应减少的第三列空间
            self.realtime_fig = Figure(figsize=(4, 3), dpi=75, facecolor='white')
            self.realtime_ax = self.realtime_fig.add_subplot(111)

            # 设置图形样式 - 调整字体大小适应较小空间
            self.realtime_ax.set_title('时域波形', fontsize=10, fontfamily='Microsoft YaHei', weight='bold')
            self.realtime_ax.set_xlabel('时间 (s)', fontsize=8, fontfamily='Microsoft YaHei')
            self.realtime_ax.set_ylabel('振动幅值', fontsize=8, fontfamily='Microsoft YaHei')
            self.realtime_ax.grid(True, alpha=0.3)

            # 调整刻度标签字体大小
            self.realtime_ax.tick_params(labelsize=7)

            # 初始化空的线条
            self.realtime_line, = self.realtime_ax.plot([], [], 'b-', linewidth=1.2, alpha=0.8)

            # 设置初始显示范围
            self.realtime_ax.set_xlim(0, 1)
            self.realtime_ax.set_ylim(-5, 5)

            # 创建画布
            self.realtime_canvas = FigureCanvasTkAgg(self.realtime_fig, parent)
            self.realtime_canvas.draw()
            self.realtime_canvas.get_tk_widget().pack(fill=BOTH, expand=True)

            # 调整布局
            self.realtime_fig.tight_layout()

        except Exception as e:
            print(f"设置实时绘图失败: {e}")
            # 如果matplotlib设置失败，显示提示信息
            Label(parent, text=f"实时显示初始化失败: {str(e)}",
                  font=("微软雅黑", 10), fg='red', bg='white').pack(expand=True)

    def update_realtime_plot(self, new_data, fs):
        """更新实时绘图 - 支持累积显示"""
        try:
            if self.realtime_ax is None or self.realtime_line is None:
                return

            # 添加新数据到缓冲区
            self.realtime_data_buffer.extend(new_data)

            # 限制缓冲区大小，避免界面卡顿
            max_points = 20000  # 增加显示点数以显示更多数据
            if len(self.realtime_data_buffer) > max_points:
                # 保留最新的数据点
                self.realtime_data_buffer = self.realtime_data_buffer[-max_points:]

            # 生成时间轴 - 基于累积的数据点数
            time_axis = np.arange(len(self.realtime_data_buffer)) / fs

            # 更新线条数据
            self.realtime_line.set_data(time_axis, self.realtime_data_buffer)

            # 自动调节X轴范围 - 显示完整的时间范围
            if len(time_axis) > 0:
                self.realtime_ax.set_xlim(0, time_axis[-1])

            # 自动调节Y轴范围
            if len(self.realtime_data_buffer) > 0:
                data_array = np.array(self.realtime_data_buffer)
                y_min, y_max = np.min(data_array), np.max(data_array)
                y_range = y_max - y_min
                if y_range > 0:
                    margin = y_range * 0.1  # 10%余量
                    self.realtime_ax.set_ylim(y_min - margin, y_max + margin)
                else:
                    self.realtime_ax.set_ylim(-1, 1)

            # 重新绘制
            self.realtime_canvas.draw()

        except Exception as e:
            print(f"更新实时绘图失败: {e}")

    def clear_realtime_plot(self):
        """清空实时绘图"""
        try:
            if self.realtime_ax is not None and self.realtime_line is not None:
                self.realtime_data_buffer.clear()
                self.realtime_line.set_data([], [])
                self.realtime_ax.set_xlim(0, 1)
                self.realtime_ax.set_ylim(-5, 5)
                if self.realtime_canvas is not None:
                    self.realtime_canvas.draw()
        except Exception as e:
            print(f"清空实时绘图失败: {e}")

    def select_for_visualization(self, attachment, index):
        """选择附件进行可视化"""
        self.selected_attachment_index = index
        self.selected_attachment = attachment

        # 更新附件列表显示（高亮选中的附件）
        self.update_attachment_list()

        # 如果当前有选择的可视化类型，立即显示
        if hasattr(self, 'current_viz_type') and self.current_viz_type:
            self.show_attachment_visualization(attachment, self.current_viz_type)
        else:
            # 默认显示时域分析
            self.show_attachment_visualization(attachment, 'time')
            self.current_viz_type = 'time'

    def remove_attachment(self, index):
        """移除附件"""
        if 0 <= index < len(self.attachments):
            del self.attachments[index]
            self.update_attachment_list()
            self.status.set("附件已移除")

    # ============== 文件处理 ==============
    def process_file(self, file_path):
        """处理一般文件"""
        if file_path.lower().endswith('.docx'):
            return self.process_word(file_path)
        else:
            return self.process_text(file_path)

    def process_pdf(self, file_path):
        """处理PDF文件"""
        text_list = []
        try:
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                for i, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    if page_text:
                        text_list.append(f"--- 页面 {i + 1} ---\n{page_text}")
            return "\n".join(text_list)
        except Exception as e:
            return f"PDF解析失败: {str(e)}"

    def process_word(self, file_path):
        """处理Word文档"""
        try:
            doc = Document(file_path)
            sections = []
            if doc.paragraphs and doc.paragraphs[0].style.name.startswith('Heading'):
                sections.append(f"标题: {doc.paragraphs[0].text}")
            content = '\n'.join(p.text for p in doc.paragraphs if p.text.strip())
            sections.append(content)
            for i, table in enumerate(doc.tables):
                table_text = []
                for row in table.rows:
                    row_text = [cell.text for cell in row.cells]
                    table_text.append(' | '.join(row_text))
                if table_text:
                    sections.append(f"--- 表格 {i + 1} ---\n" + '\n'.join(table_text))
            return '\n\n'.join(sections)
        except Exception as e:
            return f"Word文档读取失败: {str(e)}"

    def process_text(self, file_path):
        """处理文本文件"""
        try:
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            content = None
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            if content is None:
                with open(file_path, 'rb') as f:
                    content = f.read().decode('utf-8', errors='ignore')
            return content
        except Exception as e:
            return f"文本读取失败: {str(e)}"

    def process_image(self, file_path):
        """处理图像文件"""
        try:
            img = Image.open(file_path)
            width, height = img.size
            img.thumbnail((800, 600))
            img.info['original_size'] = (width, height)
            return img
        except Exception as e:
            return f"图片加载失败: {str(e)}"

    def get_ocr_text(self, image_path):
        """从图像中提取文本"""
        try:
            with Image.open(image_path) as img:
                img = ImageOps.exif_transpose(img).convert('L')
                text = pytesseract.image_to_string(img, lang='chi_sim+eng', config='--psm 6')
                if not text.strip():
                    text = pytesseract.image_to_string(img, lang='chi_sim+eng', config='--psm 3')
                return text
        except Exception as e:
            return f"OCR识别失败: {str(e)}"

    def parse_filename_params(self, filename):
        """
        从文件名解析轴承参数
        命名格式: Vibration_[设备ID]_[轴承型号]_[转速rpm]_[采样率Hz]_[滚动体数量]_[滚动体直径mm]_[节径直径mm]_[接触角度].xlsx
        例如: Vibration_Pump01_6205_1797_12000_9_7.94_39.04_0.xlsx
        """
        try:
            # 移除扩展名
            basename = os.path.splitext(filename)[0]

            # 使用正则表达式匹配参数
            pattern = r'Vibration_([^_]+)_([^_]+)_(\d+)_(\d+)_(\d+)_([\d\.]+)_([\d\.]+)_([\d\.]+)'
            match = re.match(pattern, basename)

            if match:
                device_id = match.group(1)
                bearing_type = match.group(2)
                rpm = float(match.group(3))
                fs = float(match.group(4))
                num_balls = int(match.group(5))
                ball_diameter = float(match.group(6))
                pitch_diameter = float(match.group(7))
                contact_angle = float(match.group(8))

                # 计算轴旋转频率 (Hz)
                shaft_freq = rpm / 60.0

                # 计算故障特征频率
                contact_angle_rad = contact_angle * np.pi / 180  # 转换为弧度
                bpfi = (num_balls / 2) * (1 + ball_diameter / pitch_diameter * np.cos(contact_angle_rad)) * shaft_freq
                bpfo = (num_balls / 2) * (1 - ball_diameter / pitch_diameter * np.cos(contact_angle_rad)) * shaft_freq
                bsf = (pitch_diameter / ball_diameter) * (1 - (ball_diameter / pitch_diameter * np.cos(contact_angle_rad))**2) * shaft_freq
                ftf = (1 / 2) * (1 - ball_diameter / pitch_diameter * np.cos(contact_angle_rad)) * shaft_freq

                return {
                    "device_id": device_id,
                    "bearing_type": bearing_type,
                    "rpm": rpm,
                    "fs": fs,
                    "num_balls": num_balls,
                    "ball_diameter": ball_diameter,
                    "pitch_diameter": pitch_diameter,
                    "contact_angle": contact_angle,
                    "fault_freqs": {
                        "BPFI": bpfi,
                        "BPFO": bpfo,
                        "BSF": bsf,
                        "FTF": ftf
                    }
                }
            else:
                return None
        except Exception as e:
            print(f"解析文件名参数失败: {str(e)}")
            return None

    def process_excel_file(self, file_path):
        """
        处理Excel文件，提取振动信号数据并进行分析
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)

            # 检查数据列
            if len(df.columns) < 2:
                return f"【{os.path.basename(file_path)}】Excel文件格式错误，至少需要两列数据（时间和振动信号）"

            # 提取振动信号数据（假设在第二列）
            signal = df.iloc[:, 1].values

            # 从文件名解析参数
            filename = os.path.basename(file_path)
            params = self.parse_filename_params(filename)

            if params:
                fs = params["fs"]  # 从文件名获取采样率
                fault_freqs = params["fault_freqs"]  # 从文件名获取故障特征频率
            else:
                # 如果无法从文件名解析参数，使用默认值
                fs = 12000.0
                fault_freqs = {"BPFI": 144.19, "BPFO": 95.41, "BSF": 70.51, "FTF": 11.93}

            # 计算时域特征
            mean_val = np.mean(signal)
            std_val = np.std(signal)
            p2p_val = np.ptp(signal)
            kurt_val = self.calc_kurtosis(signal)
            rms_val = np.sqrt(np.mean(signal ** 2))
            skew_val = self.calc_skewness(signal)
            cf_val = self.calc_crest_factor(signal)
            if_val = self.calc_impulse_factor(signal)
            clf_val = self.calc_clearance_factor(signal)

            # 计算包络谱
            envelope = np.abs(scipy.signal.hilbert(signal))
            freq_axis = np.fft.rfftfreq(len(signal), 1.0 / fs)
            env_spectrum = np.abs(np.fft.rfft(envelope))

            # 获取前5个峰值
            if len(env_spectrum) < 5:
                peak_indices = np.argsort(-env_spectrum)[:len(env_spectrum)]
            else:
                peak_indices = np.argsort(-env_spectrum)[:5]

            # 执行连续小波变换
            coef, scales = self.perform_wavelet_transform(signal, wavelet='morl', level=4, fs=fs)
            wavelet_info = f"默认连续小波变换 (morl, level=4) 获得 {coef.shape[0]} 个尺度"

            # 应用理论模型分析
            if params:
                # 使用从文件名解析的故障特征频率
                theory_result = self.apply_theoretical_model_with_params(freq_axis, env_spectrum, fault_freqs, threshold=0.06)
                # 保存故障特征频率到实例变量，以便其他函数使用
                self.bearing_fault_freqs = fault_freqs
            else:
                theory_result = self.apply_theoretical_model(freq_axis, env_spectrum, threshold=0.06)

            # 构建分析文本
            time_str = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
            analysis_text = (
                f"{time_str} {filename}:\n"
                f"【{filename}】Excel数据解析:\n"
            )

            if params:
                analysis_text += (
                    f"设备ID: {params['device_id']}\n"
                    f"轴承型号: {params['bearing_type']}\n"
                    f"转速: {params['rpm']} rpm\n"
                    f"采样率: {params['fs']} Hz\n"
                    f"滚动体数量: {params['num_balls']}\n"
                    f"滚动体直径: {params['ball_diameter']} mm\n"
                    f"节径直径: {params['pitch_diameter']} mm\n"
                    f"接触角: {params['contact_angle']}°\n\n"
                )

            analysis_text += (
                f"数据长度: {len(signal)}\n"
                f"采样率: {fs} Hz\n"
                f"均值: {mean_val:.4f}\n"
                f"标准差: {std_val:.4f}\n"
                f"峰峰值: {p2p_val:.4f}\n"
                f"峭度: {kurt_val:.4f}\n"
                f"RMS: {rms_val:.4f}\n"
                f"偏度: {skew_val:.4f}\n"
                f"峰值因子(CF): {cf_val:.4f}\n"
                f"脉冲因子(IF): {if_val:.4f}\n"
                f"裕度因子(CLF): {clf_val:.4f}\n\n"
                f"包络解调(前5大峰): "
            )

            peaks_str_list = []
            for idx in peak_indices:
                f_val = freq_axis[idx]
                amp_val = env_spectrum[idx]
                peaks_str_list.append(f"{f_val:.2f}Hz({amp_val:.2f})")
            analysis_text += ", ".join(peaks_str_list) + "\n\n"

            # 添加故障特征频率信息
            if params:
                analysis_text += (
                    f"故障特征频率(从文件名解析):\n"
                    f" - BPFI: {fault_freqs['BPFI']:.2f} Hz (内圈)\n"
                    f" - BPFO: {fault_freqs['BPFO']:.2f} Hz (外圈)\n"
                    f" - BSF : {fault_freqs['BSF']:.2f} Hz (滚动体)\n"
                    f" - FTF : {fault_freqs['FTF']:.2f} Hz (保持架)\n\n"
                )
            else:
                analysis_text += (
                    "故障特征频率(示例):\n"
                    f" - BPFI: 144.19 Hz\n"
                    f" - BPFO: 95.41 Hz\n"
                    f" - BSF : 70.51 Hz\n"
                    f" - FTF : 11.93 Hz\n"
                    "请设置轴承参数以计算故障特征频率。\n"
                    '可通过"分析"菜单或可视化界面中的"轴承参数设置"按钮进行设置。\n\n'
                )

            analysis_text += wavelet_info + "\n\n"

            if theory_result.strip():
                analysis_text += theory_result

            # 准备可视化数据
            visualization_data = {
                "time_signal": signal,
                "fs": fs,
                "freq_axis": freq_axis,
                "envelope_spectrum": env_spectrum,
                "wavelet_transform": {"coef": coef, "scales": scales}
            }

            # 记录到日志文件
            try:
                with open(self.mat_feature_log_file, 'a', encoding='utf-8') as lf:
                    lf.write(analysis_text + "\n\n")
            except Exception as e:
                print(f"写入 {self.mat_feature_log_file} 失败: {e}")

            return {"analysis_text": analysis_text, "visual_data": visualization_data}

        except Exception as e:
            return f"Excel文件解析失败: {str(e)}"

    # ============== 诊断指标计算相关 ==============
    def calc_kurtosis(self, data):
        import numpy as np
        mu = np.mean(data)
        sigma = np.std(data)
        if sigma < 1e-12:
            return 0.0
        return np.mean(((data - mu) / sigma) ** 4)

    def calc_skewness(self, data):
        import numpy as np
        mu = np.mean(data)
        sigma = np.std(data)
        if sigma < 1e-12:
            return 0.0
        return np.mean(((data - mu) / sigma) ** 3)

    def calc_crest_factor(self, data):
        import numpy as np
        rms_val = np.sqrt(np.mean(np.square(data)))
        if rms_val < 1e-12:
            return 0.0
        peak_val = np.max(np.abs(data))
        return peak_val / rms_val

    def calc_impulse_factor(self, data):
        import numpy as np
        mean_abs = np.mean(np.abs(data))
        if mean_abs < 1e-12:
            return 0.0
        peak_val = np.max(np.abs(data))
        return peak_val / mean_abs

    def calc_clearance_factor(self, data):
        import numpy as np
        mean_sqrt_abs = np.mean(np.sqrt(np.abs(data)))
        if mean_sqrt_abs < 1e-12:
            return 0.0
        peak_val = np.max(np.abs(data))
        return peak_val / (mean_sqrt_abs ** 2)

    # ============== 连续小波变换功能 ==============
    def perform_wavelet_transform(self, signal, wavelet='morl', level=4, fs=1.0):
        """
        对信号执行连续小波变换
        Args:
            signal: 输入信号（一维数组）
            wavelet: 小波基名称（支持 'morl'、'cmor'、'mexh' 等）
            level: 分解层数，用于确定尺度范围（scales 从 1 到 2**level）
            fs: 采样率（用于确定采样周期）
        Returns:
            coef: 变换系数二维数组，形状为 (num_scales, len(signal))
            scales: 对应的尺度数组
        """
        import numpy as np
        scales = np.arange(1, 2**(level) + 1)
        coef, freqs = pywt.cwt(signal, scales, wavelet, sampling_period=1/fs)
        return coef, scales

    # ============== 结合传统机理模型/经验公式 ==============
    def apply_theoretical_model(self, freq_axis, env_spectrum, threshold=0.05):
        """
        应用理论模型分析频谱

        如果用户已经设置了轴承参数，则使用计算的故障特征频率
        否则提示用户设置轴承参数
        """
        # 检查是否有用户设置的故障特征频率
        if hasattr(self, 'bearing_fault_freqs'):
            # 使用用户设置的故障特征频率
            return self.apply_theoretical_model_with_params(freq_axis, env_spectrum, self.bearing_fault_freqs, threshold)
        else:
            # 提示用户设置轴承参数
            results = []
            results.append("=== 轴承故障诊断 ===")
            results.append("请设置轴承参数以进行故障诊断。")
            results.append('可通过"分析"菜单或可视化界面中的"轴承参数设置"按钮进行设置。')
            results.append("")
            return "\n".join(results)

    # ============== 解析 .mat 文件 + 包络解调 + 连续小波变换 + 机理模型 ==============
    def process_mat_file(self, file_path):
        """
        处理.mat文件，执行信号分析，包括包络解调与连续小波变换
        """
        try:
            import numpy as np
            mat_data = scipy.io.loadmat(file_path)
            valid_keys = [k for k in mat_data.keys() if not k.startswith('__')]
            target_key = None
            for k in valid_keys:
                if '_time' in k.lower():
                    target_key = k
                    break
            if not target_key:
                for k in valid_keys:
                    if isinstance(mat_data[k], np.ndarray) and mat_data[k].size > 100:
                        target_key = k
                        break
            if not target_key:
                return f"【{os.path.basename(file_path)}】未找到合适的数据变量，请检查文件。"
            filename = os.path.basename(file_path)
            signal = mat_data[target_key].ravel()
            mean_val = np.mean(signal)
            std_val = np.std(signal)
            p2p_val = np.ptp(signal)
            kurt_val = self.calc_kurtosis(signal)
            rms_val = np.sqrt(np.mean(signal ** 2))
            skew_val = self.calc_skewness(signal)
            cf_val = self.calc_crest_factor(signal)
            if_val = self.calc_impulse_factor(signal)
            clf_val = self.calc_clearance_factor(signal)
            envelope = np.abs(scipy.signal.hilbert(signal))
            fs = 12000.0
            if 'fs' in valid_keys and np.isscalar(mat_data['fs']):
                fs = float(mat_data['fs'])
            elif 'Fs' in valid_keys and np.isscalar(mat_data['Fs']):
                fs = float(mat_data['Fs'])
            freq_axis = np.fft.rfftfreq(len(signal), 1.0 / fs)
            env_spectrum = np.abs(np.fft.rfft(envelope))
            if len(env_spectrum) < 5:
                peak_indices = np.argsort(-env_spectrum)[:len(env_spectrum)]
            else:
                peak_indices = np.argsort(-env_spectrum)[:5]
            # 默认连续小波参数：wavelet 'morl', level=4（尺度范围 1~16）
            coef, scales = self.perform_wavelet_transform(signal, wavelet='morl', level=4, fs=fs)
            wavelet_info = f"默认连续小波变换 (morl, level=4) 获得 {coef.shape[0]} 个尺度"
            time_str = datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
            theory_result = self.apply_theoretical_model(freq_axis, env_spectrum, threshold=0.06)
            analysis_text = (
                f"{time_str} {filename}:\n"
                f"【{filename}】数据解析:\n"
                f"解析到变量: {target_key}\n"
                f"数据长度: {len(signal)}\n"
                f"采样率: {fs} Hz\n"
                f"均值: {mean_val:.4f}\n"
                f"标准差: {std_val:.4f}\n"
                f"峰峰值: {p2p_val:.4f}\n"
                f"峭度: {kurt_val:.4f}\n"
                f"RMS: {rms_val:.4f}\n"
                f"偏度: {skew_val:.4f}\n"
                f"峰值因子(CF): {cf_val:.4f}\n"
                f"脉冲因子(IF): {if_val:.4f}\n"
                f"裕度因子(CLF): {clf_val:.4f}\n\n"
                f"包络解调(前5大峰): "
            )
            peaks_str_list = []
            for idx in peak_indices:
                f_val = freq_axis[idx]
                amp_val = env_spectrum[idx]
                peaks_str_list.append(f"{f_val:.2f}Hz({amp_val:.2f})")
            analysis_text += ", ".join(peaks_str_list) + "\n\n"
            analysis_text += (
                "请设置轴承参数以计算故障特征频率。\n"
                '可通过"分析"菜单或可视化界面中的"轴承参数设置"按钮进行设置。\n\n'
            )
            analysis_text += wavelet_info + "\n\n"
            if theory_result.strip():
                analysis_text += theory_result
            visualization_data = {
                "time_signal": signal,
                "fs": fs,
                "freq_axis": freq_axis,
                "envelope_spectrum": env_spectrum,
                "wavelet_transform": {"coef": coef, "scales": scales}  # 保存连续小波变换结果
            }
            try:
                with open(self.mat_feature_log_file, 'a', encoding='utf-8') as lf:
                    lf.write(analysis_text + "\n\n")
            except Exception as e:
                print(f"写入 {self.mat_feature_log_file} 失败: {e}")
            return {"analysis_text": analysis_text, "visual_data": visualization_data}
        except Exception as e:
            return f".mat 文件解析失败: {str(e)}"

    # ============== 附件可视化相关 ==============
    def show_preview(self, attachment):
        """显示附件预览"""
        self.close_preview()
        self.preview_window = Toplevel(self.master)
        self.preview_window.title(f"文件预览 - {attachment['name']}")
        self.preview_window.geometry("800x600")
        container = ttk.Frame(self.preview_window)
        container.pack(fill=BOTH, expand=True, padx=10, pady=10)
        control_frame = ttk.Frame(container)
        control_frame.pack(fill=X, pady=5)
        ttk.Button(control_frame, text="切换视图", command=lambda: self.toggle_preview_mode(attachment)).pack(side=LEFT)
        ttk.Button(control_frame, text="关闭预览", command=self.close_preview).pack(side=RIGHT)
        if attachment['is_image']:
            ttk.Button(control_frame, text="保存OCR结果", command=lambda: self.save_ocr_result(attachment)).pack(side=RIGHT, padx=5)
        self.content_frame = ttk.Frame(container)
        self.content_frame.pack(fill=BOTH, expand=True)
        self.update_preview_content(attachment)

    def save_ocr_result(self, attachment):
        """保存OCR结果"""
        if not attachment.get('ocr_text'):
            messagebox.showinfo("OCR", "没有可保存的OCR结果")
            return
        file_path = filedialog.asksaveasfilename(defaultextension=".txt",
                                                 filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                                                 initialfile=f"{os.path.splitext(attachment['name'])[0]}_ocr.txt",
                                                 initialdir=self.work_dir,  # 默认保存到deepseek助手文件夹
                                                 title="保存OCR结果")
        if not file_path:
            return
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(attachment['ocr_text'])
            self.status.set(f"OCR结果已保存至 {os.path.basename(file_path)}")
        except Exception as e:
            self.show_error(f"保存OCR结果失败: {str(e)}")

    def close_preview(self):
        """关闭预览窗口"""
        if self.preview_window:
            self.preview_window.destroy()
            self.preview_window = None
        self.current_image = None

    def toggle_preview_mode(self, attachment):
        """切换预览模式"""
        self.preview_type = "text" if self.preview_type == "image" else "image"
        self.update_preview_content(attachment)

    def update_preview_content(self, attachment):
        """更新预览内容"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        if isinstance(attachment['content'], dict):
            content = attachment['content'].get('analysis_text', '无可预览文本')
            is_image = False
        else:
            content = attachment['content']
            is_image = attachment['is_image']
        if self.preview_type == "image" and is_image:
            self.show_image_preview(content)
        else:
            self.show_text_preview(attachment, content)

    def show_image_preview(self, image):
        """显示图像预览"""
        try:
            if isinstance(image, Image.Image):
                size_info = ""
                if hasattr(image, 'info') and 'original_size' in image.info:
                    w, h = image.info['original_size']
                    size_info = f"原始分辨率: {w}x{h}"
                if size_info:
                    ttk.Label(self.content_frame, text=size_info).pack(anchor=W, padx=5, pady=5)
                self.current_image = ImageTk.PhotoImage(image)
                canvas = Canvas(self.content_frame)
                scroll_x = ttk.Scrollbar(self.content_frame, orient="horizontal", command=canvas.xview)
                scroll_y = ttk.Scrollbar(self.content_frame, orient="vertical", command=canvas.yview)
                canvas.configure(xscrollcommand=scroll_x.set, yscrollcommand=scroll_y.set)
                scroll_x.pack(side=BOTTOM, fill=X)
                scroll_y.pack(side=RIGHT, fill=Y)
                canvas.pack(side=LEFT, fill=BOTH, expand=True)
                canvas.create_image(0, 0, anchor="nw", image=self.current_image)
                canvas.config(scrollregion=canvas.bbox("all"))
        except Exception as e:
            messagebox.showerror("图片预览错误", f"无法加载图片: {str(e)}")

    def show_text_preview(self, attachment, text):
        """显示文本预览"""
        text_frame = scrolledtext.ScrolledText(self.content_frame, wrap=WORD, font=("Consolas", 10), padx=5, pady=5)
        text_frame.insert(END, text)
        text_frame.configure(state='disabled')
        text_frame.pack(fill=BOTH, expand=True)
        if attachment['is_image'] and self.preview_type == "text":
            ttk.Label(self.content_frame, text="显示的是OCR识别结果", foreground="blue").pack(side=BOTTOM, anchor=E, padx=5, pady=2)





    def show_bearing_params_dialog(self, attachment=None):
        """显示轴承参数输入对话框"""
        params_window = Toplevel(self.master)
        params_window.title("轴承参数设置")
        params_window.geometry("500x400")
        params_window.transient(self.master)
        params_window.grab_set()

        # 创建参数输入框
        ttk.Label(params_window, text="轴承参数设置", font=("微软雅黑", 12, "bold")).grid(row=0, column=0, columnspan=2, pady=10, padx=10, sticky=W)

        # 轴承基本参数
        ttk.Label(params_window, text="轴承型号:").grid(row=1, column=0, padx=10, pady=5, sticky=W)
        bearing_type_var = StringVar(value="6205")
        ttk.Entry(params_window, textvariable=bearing_type_var, width=15).grid(row=1, column=1, padx=10, pady=5, sticky=W+E)

        ttk.Label(params_window, text="滚动体数量 (N):").grid(row=2, column=0, padx=10, pady=5, sticky=W)
        num_balls_var = StringVar(value="9")
        ttk.Entry(params_window, textvariable=num_balls_var, width=15).grid(row=2, column=1, padx=10, pady=5, sticky=W+E)

        ttk.Label(params_window, text="滚动体直径 (Bd, mm):").grid(row=3, column=0, padx=10, pady=5, sticky=W)
        ball_diameter_var = StringVar(value="7.94")
        ttk.Entry(params_window, textvariable=ball_diameter_var, width=15).grid(row=3, column=1, padx=10, pady=5, sticky=W+E)

        ttk.Label(params_window, text="节径直径 (Pd, mm):").grid(row=4, column=0, padx=10, pady=5, sticky=W)
        pitch_diameter_var = StringVar(value="39.04")
        ttk.Entry(params_window, textvariable=pitch_diameter_var, width=15).grid(row=4, column=1, padx=10, pady=5, sticky=W+E)

        ttk.Label(params_window, text="接触角 (α, 度):").grid(row=5, column=0, padx=10, pady=5, sticky=W)
        contact_angle_var = StringVar(value="0")
        ttk.Entry(params_window, textvariable=contact_angle_var, width=15).grid(row=5, column=1, padx=10, pady=5, sticky=W+E)

        ttk.Label(params_window, text="轴转速 (rpm):").grid(row=6, column=0, padx=10, pady=5, sticky=W)
        shaft_speed_var = StringVar(value="1797")
        ttk.Entry(params_window, textvariable=shaft_speed_var, width=15).grid(row=6, column=1, padx=10, pady=5, sticky=W+E)

        # 采样率设置
        ttk.Label(params_window, text="采样率 (Hz):").grid(row=7, column=0, padx=10, pady=5, sticky=W)
        sampling_rate_var = StringVar(value="12000")
        ttk.Entry(params_window, textvariable=sampling_rate_var, width=15).grid(row=7, column=1, padx=10, pady=5, sticky=W+E)

        # 计算结果显示区域
        ttk.Label(params_window, text="计算结果:", font=("微软雅黑", 10, "bold")).grid(row=8, column=0, columnspan=2, pady=5, padx=10, sticky=W)
        result_text = scrolledtext.ScrolledText(params_window, height=6, width=40, wrap=WORD)
        result_text.grid(row=9, column=0, columnspan=2, padx=10, pady=5, sticky=W+E)
        result_text.configure(state='disabled')

        def calculate_fault_frequencies():
            try:
                # 获取参数值
                num_balls = float(num_balls_var.get())
                ball_diameter = float(ball_diameter_var.get())
                pitch_diameter = float(pitch_diameter_var.get())
                contact_angle = float(contact_angle_var.get()) * np.pi / 180  # 转换为弧度
                shaft_speed = float(shaft_speed_var.get())

                # 计算轴旋转频率 (Hz)
                shaft_freq = shaft_speed / 60.0

                # 计算故障特征频率
                bpfi = (num_balls / 2) * (1 + ball_diameter / pitch_diameter * np.cos(contact_angle)) * shaft_freq
                bpfo = (num_balls / 2) * (1 - ball_diameter / pitch_diameter * np.cos(contact_angle)) * shaft_freq
                bsf = (pitch_diameter / ball_diameter) * (1 - (ball_diameter / pitch_diameter * np.cos(contact_angle))**2) * shaft_freq
                ftf = (1 / 2) * (1 - ball_diameter / pitch_diameter * np.cos(contact_angle)) * shaft_freq

                # 显示结果
                result_text.configure(state='normal')
                result_text.delete(1.0, END)
                result_text.insert(END, f"内圈故障频率 (BPFI): {bpfi:.2f} Hz\n")
                result_text.insert(END, f"外圈故障频率 (BPFO): {bpfo:.2f} Hz\n")
                result_text.insert(END, f"滚动体故障频率 (BSF): {bsf:.2f} Hz\n")
                result_text.insert(END, f"保持架故障频率 (FTF): {ftf:.2f} Hz\n")
                result_text.configure(state='disabled')

                # 保存计算结果
                self.bearing_fault_freqs = {
                    "BPFI": bpfi,
                    "BPFO": bpfo,
                    "BSF": bsf,
                    "FTF": ftf
                }

                # 如果提供了附件，更新其分析文本
                if attachment:
                    self.update_attachment_with_fault_freqs(attachment)

            except ValueError as e:
                messagebox.showerror("输入错误", "请输入有效的数值")

        def save_and_close():
            calculate_fault_frequencies()
            params_window.destroy()

        # 按钮区域
        button_frame = ttk.Frame(params_window)
        button_frame.grid(row=10, column=0, columnspan=2, pady=10)
        ttk.Button(button_frame, text="计算", command=calculate_fault_frequencies).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="保存并关闭", command=save_and_close).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=params_window.destroy).pack(side=LEFT, padx=5)

        # 预先计算一次
        calculate_fault_frequencies()

        return params_window

    def show_wind_turbine_params_dialog(self, attachment=None):
        """显示风机参数输入对话框"""
        params_window = Toplevel(self.master)
        params_window.title("风机参数设置")
        params_window.geometry("500x450")
        params_window.transient(self.master)
        params_window.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(params_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # 标题
        title_label = ttk.Label(main_frame, text="风机故障特征频率计算",
                               font=("微软雅黑", 12, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 参数输入区域
        params_frame = ttk.LabelFrame(main_frame, text="风机参数", padding=10)
        params_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))

        # 参数变量
        rpm_var = StringVar(value="1800")  # 转速 RPM
        blade_count_var = StringVar(value="3")  # 叶片数

        # 参数输入控件
        param_info = {
            'rpm': ('转子转速 (RPM)', rpm_var, '风机转子的旋转速度'),
            'blade_count': ('叶片数量', blade_count_var, '风机叶片的数量，通常为3')
        }

        for i, (param_key, (label_text, var, tooltip)) in enumerate(param_info.items()):
            ttk.Label(params_frame, text=label_text + ":").grid(row=i, column=0, sticky=W, pady=2)
            entry = ttk.Entry(params_frame, textvariable=var, width=15)
            entry.grid(row=i, column=1, sticky=W, padx=(10, 0), pady=2)

            # 添加工具提示标签
            tooltip_label = ttk.Label(params_frame, text=f"({tooltip})",
                                    font=("微软雅黑", 8), foreground="gray")
            tooltip_label.grid(row=i, column=2, sticky=W, padx=(10, 0), pady=2)

        # 结果显示区域
        results_frame = ttk.LabelFrame(main_frame, text="计算结果", padding=10)
        results_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))

        # 结果文本框
        results_text = Text(results_frame, height=12, width=60, font=("Consolas", 9))
        results_text.grid(row=0, column=0, sticky="ew")

        # 滚动条
        scrollbar = ttk.Scrollbar(results_frame, orient=VERTICAL, command=results_text.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        results_text.config(yscrollcommand=scrollbar.set)

        def calculate_wind_turbine_frequencies():
            try:
                # 获取参数值
                rpm = float(rpm_var.get())
                blade_count = int(blade_count_var.get())

                # 计算风机故障特征频率
                fault_freqs = self.calculate_wind_turbine_fault_frequencies(
                    rpm, blade_count
                )

                # 显示结果
                results_text.delete(1.0, END)
                results_text.insert(END, "风机故障特征频率计算结果:\n")
                results_text.insert(END, "=" * 40 + "\n\n")

                for fault_type, freq_value in fault_freqs.items():
                    results_text.insert(END, f"{fault_type:15}: {freq_value}\n")

                results_text.insert(END, "\n" + "=" * 40 + "\n")
                results_text.insert(END, "故障诊断指导:\n")
                results_text.insert(END, "• 转子不平衡: 主要表现为1X转频及其谐波\n")
                results_text.insert(END, "• 转子不对中: 主要表现为2X转频及其谐波\n")
                results_text.insert(END, "• 叶片故障: 表现为叶片通过频率及其谐波\n")

                # 保存故障特征频率到实例变量
                self.wind_turbine_fault_freqs = fault_freqs

                # 如果有附件，更新附件的分析文本
                if attachment:
                    self.update_attachment_with_wind_turbine_fault_freqs(attachment)

            except ValueError:
                messagebox.showerror("输入错误", "请输入有效的数值")

        def save_and_close():
            calculate_wind_turbine_frequencies()
            params_window.destroy()

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=10)
        ttk.Button(button_frame, text="计算", command=calculate_wind_turbine_frequencies).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="保存并关闭", command=save_and_close).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=params_window.destroy).pack(side=LEFT, padx=5)

        # 预先计算一次
        calculate_wind_turbine_frequencies()

        return params_window

    def update_attachment_with_wind_turbine_fault_freqs(self, attachment):
        """使用计算的风机故障特征频率更新附件的分析文本"""
        if not hasattr(self, 'wind_turbine_fault_freqs') or not isinstance(attachment['content'], dict):
            return

        if 'analysis_text' in attachment['content']:
            # 查找并替换故障特征频率部分
            old_text = attachment['content']['analysis_text']
            new_text = old_text

            # 如果有频谱数据，重新应用理论模型
            if 'freq_axis' in attachment['content'] and 'envelope_spectrum' in attachment['content']:
                data = attachment['content']
                theory_result = self.apply_theoretical_model_with_params(
                    data['freq_axis'],
                    data['envelope_spectrum'],
                    self.wind_turbine_fault_freqs,
                    threshold=0.06
                )

                # 替换旧的理论分析结果
                theory_start = new_text.find("=== 基于传统机理模型推断 ===")
                if theory_start != -1:
                    theory_end = new_text.find("\n\n", theory_start)
                    if theory_end != -1:
                        new_text = new_text[:theory_start] + theory_result + new_text[theory_end:]
                        attachment['content']['analysis_text'] = new_text

    def update_attachment_with_fault_freqs(self, attachment):
        """使用计算的故障特征频率更新附件的分析文本"""
        if not hasattr(self, 'bearing_fault_freqs') or not isinstance(attachment['content'], dict):
            return

        if 'analysis_text' in attachment['content']:
            # 查找并替换故障特征频率部分
            old_text = attachment['content']['analysis_text']
            new_text = old_text

            # 查找故障特征频率部分 - 可能是示例频率或提示信息
            freq_section_start = old_text.find("请设置轴承参数以计算故障特征频率")
            if freq_section_start == -1:
                freq_section_start = old_text.find("故障特征频率(示例):")

            if freq_section_start != -1:
                freq_section_end = old_text.find("\n\n", freq_section_start)
                if freq_section_end != -1:
                    # 构建新的故障特征频率文本
                    new_freq_text = (
                        "故障特征频率(计算值):\n"
                        f" - BPFI: {self.bearing_fault_freqs['BPFI']:.2f} Hz (内圈)\n"
                        f" - BPFO: {self.bearing_fault_freqs['BPFO']:.2f} Hz (外圈)\n"
                        f" - BSF : {self.bearing_fault_freqs['BSF']:.2f} Hz (滚动体)\n"
                        f" - FTF : {self.bearing_fault_freqs['FTF']:.2f} Hz (保持架)\n"
                        "可进一步对比峰值判断故障类型。"
                    )

                    # 替换文本
                    new_text = old_text[:freq_section_start] + new_freq_text + old_text[freq_section_end:]
                    attachment['content']['analysis_text'] = new_text

            # 更新理论模型分析
            if 'visual_data' in attachment['content']:
                data = attachment['content']['visual_data']
                if 'freq_axis' in data and 'envelope_spectrum' in data:
                    # 使用新的故障特征频率重新分析
                    theory_result = self.apply_theoretical_model_with_params(
                        data['freq_axis'],
                        data['envelope_spectrum'],
                        self.bearing_fault_freqs,
                        threshold=0.06
                    )

                    # 替换旧的理论分析结果
                    theory_start = new_text.find("=== 基于传统机理模型推断 ===")
                    if theory_start != -1:
                        theory_end = new_text.find("\n\n", theory_start)
                        if theory_end != -1:
                            new_text = new_text[:theory_start] + theory_result + new_text[theory_end:]
                            attachment['content']['analysis_text'] = new_text

    def apply_theoretical_model_with_params(self, freq_axis, env_spectrum, fault_freqs, threshold=0.05):
        """使用提供的故障特征频率应用理论模型"""
        import numpy as np
        results = []
        peak_indices = np.argsort(-env_spectrum)[:5]
        found_faults = []

        for idx in peak_indices:
            peak_f = freq_axis[idx]
            for name, f_val_str in fault_freqs.items():
                # 处理频率值，提取数值部分
                if isinstance(f_val_str, str):
                    if f_val_str == "N/A":
                        continue
                    f_val = float(f_val_str.replace('Hz', ''))
                else:
                    f_val = float(f_val_str)

                # 基频匹配
                if abs(peak_f - f_val) / f_val < threshold:
                    found_faults.append((peak_f, name, "基频"))

                # 谐波匹配
                for harmonic in range(2, 5):
                    if abs(peak_f - f_val * harmonic) / (f_val * harmonic) < threshold:
                        found_faults.append((peak_f, name, f"{harmonic}次谐波"))

        if found_faults:
            results.append("=== 基于传统机理模型推断 ===")
            for pf, nm, hm in found_faults:
                # 根据故障类型提供更详细的诊断信息
                fault_description = self.get_fault_description(nm)
                results.append(f"峰值 {pf:.2f}Hz 接近 {nm} ({hm}) => 存在潜在 {fault_description}")
            results.append("")

        return "\n".join(results)

    def get_fault_description(self, fault_name):
        """根据故障名称返回详细描述"""
        fault_descriptions = {
            # 轴承故障
            '外圈': '外圈故障',
            '内圈': '内圈故障',
            '滚动体': '滚动体故障',
            '保持架': '保持架故障',

            # 风机故障
            '转子转频': '转子基频异常',
            '叶片通过': '叶片通过频率异常',
            '转子不平衡1X': '转子不平衡故障(1X)',
            '转子不平衡2X': '转子不平衡故障(2X)',
            '转子不对中2X': '转子不对中故障(2X)',
            '转子不对中4X': '转子不对中故障(4X)',
            '叶片故障1X': '叶片故障(基频)',
            '叶片故障2X': '叶片故障(2次谐波)',
            '齿轮啮合': '齿轮箱故障',
            '电气频率': '发电机电气故障'
        }
        return fault_descriptions.get(fault_name, f'{fault_name}故障')

    def show_visualization(self, attachment):
        """显示数据可视化，并提供交互式连续小波参数调节（显示时频图）"""
        import numpy as np
        data_dict = attachment['content']['visual_data']
        signal = data_dict["time_signal"]
        fs = data_dict["fs"]
        freq_axis = data_dict["freq_axis"]
        env_spectrum = data_dict["envelope_spectrum"]
        viz_window = Toplevel(self.master)
        viz_window.title(f"可视化 - {attachment['name']}")
        viz_window.geometry("1200x900")  # 增大窗口尺寸
        fig = Figure(figsize=(10, 9), dpi=100)  # 增大图形尺寸
        ax_time = fig.add_subplot(3, 1, 1)
        t = np.arange(len(signal)) / fs
        ax_time.plot(t, signal, linewidth=0.8)
        ax_time.set_title("时域波形", fontfamily='Microsoft YaHei', fontsize=10)  # 减小字体
        ax_time.set_xlabel("时间 (s)", fontfamily='Microsoft YaHei', fontsize=8)
        ax_time.set_ylabel("振动幅值", fontfamily='Microsoft YaHei', fontsize=8)  # 统一y轴标题
        ax_time.tick_params(labelsize=7)  # 减小刻度标签字体
        ax_time.grid(True, alpha=0.3)

        ax_env = fig.add_subplot(3, 1, 2)
        ax_env.plot(freq_axis, env_spectrum, linewidth=0.8)
        ax_env.set_title("包络谱", fontfamily='Microsoft YaHei', fontsize=10)
        ax_env.set_xlabel("频率 (Hz)", fontfamily='Microsoft YaHei', fontsize=8)
        ax_env.set_ylabel("振动幅值", fontfamily='Microsoft YaHei', fontsize=8)  # 统一y轴标题
        ax_env.tick_params(labelsize=7)

        # 包络谱完整显示 - 不进行自适应调节
        # 显示完整的频率范围和幅值范围
        ax_env.set_xlim(0, freq_axis[-1])  # 显示完整频率范围
        ax_env.set_ylim(0, np.max(env_spectrum) * 1.05)  # 显示完整幅值范围，留5%余量
        ax_env.grid(True, alpha=0.3)
        default_wavelet = 'morl'
        default_level = 4
        coef, scales = self.perform_wavelet_transform(signal, wavelet=default_wavelet, level=default_level, fs=fs)
        ax_cwt = fig.add_subplot(3, 1, 3)
        im = ax_cwt.imshow(np.abs(coef), extent=[t[0], t[-1], scales[-1], scales[0]],
                           aspect='auto', cmap='jet')
        ax_cwt.set_title(f"连续小波变换时频图 ({default_wavelet}, level={default_level})",
                        fontfamily='Microsoft YaHei', fontsize=10)
        ax_cwt.set_xlabel("时间 (s)", fontfamily='Microsoft YaHei', fontsize=8)
        ax_cwt.set_ylabel("频率尺度", fontfamily='Microsoft YaHei', fontsize=8)  # 调整y轴标题
        ax_cwt.tick_params(labelsize=7)
        # 调整子图间距，使布局更紧凑，并统一y轴标题位置
        fig.tight_layout(pad=1.0, h_pad=0.8, rect=[0.08, 0, 1, 1])  # 左侧预留8%空间给y轴标题

        # 统一y轴标题位置 - 设置相同的标签位置
        for ax in [ax_time, ax_env, ax_cwt]:
            ax.yaxis.set_label_coords(-0.08, 0.5)  # 统一y轴标题位置
        canvas = FigureCanvasTkAgg(fig, master=viz_window)
        canvas.draw()
        canvas.get_tk_widget().pack(side=TOP, fill=BOTH, expand=True)

        # 为每个子图添加缩放功能
        self.setup_multi_axis_zoom(canvas, [ax_time, ax_env, ax_cwt], fig)

        toolbar = NavigationToolbar2Tk(canvas, viz_window)
        toolbar.update()
        canvas.get_tk_widget().pack(side=TOP, fill=BOTH, expand=True)
        ctrl_frame = ttk.Frame(viz_window)
        ctrl_frame.pack(side=TOP, fill=X, padx=5, pady=5)

        # 添加轴承参数设置按钮
        ttk.Button(ctrl_frame, text="轴承参数设置", command=lambda: self.show_bearing_params_dialog(attachment)).pack(side=LEFT, padx=5)

        Label(ctrl_frame, text="Wavelet:").pack(side=LEFT, padx=2)
        wavelet_var = StringVar(value=default_wavelet)
        wavelet_combo = ttk.Combobox(ctrl_frame, textvariable=wavelet_var, values=["morl", "cmor", "mexh"], state="readonly", width=8)
        wavelet_combo.pack(side=LEFT, padx=2)
        Label(ctrl_frame, text="Level:").pack(side=LEFT, padx=2)
        level_var = StringVar(value=str(default_level))
        Entry(ctrl_frame, textvariable=level_var, width=5).pack(side=LEFT, padx=2)
        def update_wavelet():
            try:
                wavelet_choice = wavelet_var.get()
                level_choice = int(level_var.get())
                new_coef, new_scales = self.perform_wavelet_transform(signal, wavelet=wavelet_choice, level=level_choice, fs=fs)
                im.set_data(np.abs(new_coef))
                new_extent = [t[0], t[-1], new_scales[-1], new_scales[0]]
                im.set_extent(new_extent)
                ax_cwt.set_title(f"连续小波变换时频图 ({wavelet_choice}, level={level_choice})",
                                fontfamily='Microsoft YaHei', fontsize=12)
                canvas.draw()
            except Exception as ex:
                messagebox.showerror("更新错误", f"更新小波变换失败: {str(ex)}")
        ttk.Button(ctrl_frame, text="应用小波参数", command=update_wavelet).pack(side=LEFT, padx=5)
        Label(ctrl_frame, text="xMin:").pack(side=LEFT, padx=2)
        xMinVar = StringVar(value="0")
        Entry(ctrl_frame, textvariable=xMinVar, width=8).pack(side=LEFT, padx=2)
        Label(ctrl_frame, text="xMax:").pack(side=LEFT, padx=2)
        xMaxVar = StringVar(value=f"{freq_axis[-1]:.2f}")
        Entry(ctrl_frame, textvariable=xMaxVar, width=8).pack(side=LEFT, padx=2)
        Label(ctrl_frame, text="yMin:").pack(side=LEFT, padx=2)
        yMinVar = StringVar(value="0")
        Entry(ctrl_frame, textvariable=yMinVar, width=8).pack(side=LEFT, padx=2)
        Label(ctrl_frame, text="yMax:").pack(side=LEFT, padx=2)
        yMaxVar = StringVar(value=f"{float(np.max(env_spectrum)) * 1.1:.2f}")
        Entry(ctrl_frame, textvariable=yMaxVar, width=8).pack(side=LEFT, padx=2)
        def apply_axis():
            try:
                x_min = float(xMinVar.get())
                x_max = float(xMaxVar.get())
                y_min = float(yMinVar.get())
                y_max = float(yMaxVar.get())
                ax_env.set_xlim(left=x_min, right=x_max)
                ax_env.set_ylim(bottom=y_min, top=y_max)
                canvas.draw()
            except ValueError:
                messagebox.showerror("错误", "请输入正确的数值")
        ttk.Button(ctrl_frame, text="Apply", command=apply_axis).pack(side=LEFT, padx=5)
        def save_figure():
            path = filedialog.asksaveasfilename(title="Save Visualization", defaultextension=".png",
                                                filetypes=[("PNG Image", "*.png"), ("All Files", "*.*")],
                                                initialdir=self.work_dir)  # 默认保存到deepseek助手文件夹
            if path:
                fig.savefig(path)
                messagebox.showinfo("Saved", f"Figure saved to: {path}")
        ttk.Button(ctrl_frame, text="Save", command=save_figure).pack(side=LEFT, padx=5)

    # ============== 深度学习故障诊断 ==============
    def show_deep_learning_window(self):
        """显示深度学习故障诊断功能 - 切换到深度学习右侧面板"""
        if not DL_AVAILABLE:
            messagebox.showerror("功能不可用",
                               "深度学习功能不可用！\n\n请确保已安装以下依赖：\n"
                               "- torch\n"
                               "- scikit-learn\n\n"
                               "安装命令：pip install torch scikit-learn")
            return

        # 切换到深度学习右侧面板
        self.show_right_panel("deep_learning")



    def create_data_loading_tab(self):
        """创建数据加载选项卡"""
        # 创建选项卡框架
        data_frame = Frame(self.dl_notebook, bg='white')
        self.dl_notebook.add(data_frame, text="📁 数据加载")

        # 创建滚动区域
        canvas = Canvas(data_frame, bg='white')
        scrollbar = ttk.Scrollbar(data_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = Frame(canvas, bg='white')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # === 数据目录选择 ===
        dir_section = LabelFrame(scrollable_frame, text="📂 数据目录选择",
                                font=("微软雅黑", 12, "bold"),
                                bg='white', fg='black')
        dir_section.pack(fill=X, padx=20, pady=10)

        # 目录选择框架
        dir_frame = Frame(dir_section, bg='white')
        dir_frame.pack(fill=X, padx=15, pady=10)

        Label(dir_frame, text="数据目录:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)

        dir_input_frame = Frame(dir_frame, bg='white')
        dir_input_frame.pack(fill=X, pady=5)

        self.dl_data_dir_entry = Entry(dir_input_frame, textvariable=self.dl_data_dir_var,
                                      font=("微软雅黑", 10), width=60, bg='white', fg='black')
        self.dl_data_dir_entry.pack(side=LEFT, fill=X, expand=True, padx=(0, 10))

        Button(dir_input_frame, text="📁 浏览", font=("微软雅黑", 9),
               bg=self.colors['primary'], fg='white', relief='flat',
               command=self.select_data_directory).pack(side=RIGHT)

        # 目录结构显示
        self.dir_tree_frame = Frame(dir_section, bg='white')
        self.dir_tree_frame.pack(fill=BOTH, expand=True, padx=15, pady=5)

        # === 采样设置 ===
        sampling_section = LabelFrame(scrollable_frame, text="⚙️ 采样设置",
                                     font=("微软雅黑", 12, "bold"),
                                     bg='white', fg='black')
        sampling_section.pack(fill=X, padx=20, pady=10)

        # 采样参数框架 - 两列布局
        sampling_frame = Frame(sampling_section, bg='white')
        sampling_frame.pack(fill=X, padx=15, pady=10)

        # 左列
        left_col = Frame(sampling_frame, bg='white')
        left_col.pack(side=LEFT, fill=X, expand=True, padx=(0, 20))

        # 样本长度
        Label(left_col, text="样本长度:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)
        Entry(left_col, textvariable=self.dl_sample_length_var,
              font=("微软雅黑", 10), width=20, bg='white', fg='black').pack(anchor=W, pady=(2, 10))

        # 步长
        Label(left_col, text="步长:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)
        Entry(left_col, textvariable=self.dl_stride_var,
              font=("微软雅黑", 10), width=20, bg='white', fg='black').pack(anchor=W, pady=(2, 10))

        # 右列
        right_col = Frame(sampling_frame, bg='white')
        right_col.pack(side=RIGHT, fill=X, expand=True)

        # 重叠率
        Label(right_col, text="重叠率:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)
        Entry(right_col, textvariable=self.dl_overlap_rate_var,
              font=("微软雅黑", 10), width=20, bg='white', fg='black').pack(anchor=W, pady=(2, 10))

        # 每类样本数
        Label(right_col, text="每类样本数:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)
        Entry(right_col, textvariable=self.dl_samples_per_class_var,
              font=("微软雅黑", 10), width=20, bg='white', fg='black').pack(anchor=W, pady=(2, 10))

        # === 数据加载控制 ===
        control_section = LabelFrame(scrollable_frame, text="🎛️ 数据加载控制",
                                    font=("微软雅黑", 12, "bold"),
                                    bg='white', fg='black')
        control_section.pack(fill=X, padx=20, pady=10)

        control_frame = Frame(control_section, bg='white')
        control_frame.pack(fill=X, padx=15, pady=10)

        # 加载数据按钮
        Button(control_frame, text="📊 加载数据", font=("微软雅黑", 11, "bold"),
               bg=self.colors['success'], fg='white', relief='flat',
               padx=20, pady=8, command=self.load_dl_data).pack(side=LEFT, padx=(0, 10))

        # 数据信息显示
        self.dl_data_info_label = Label(control_frame, text="未加载数据",
                                       font=("微软雅黑", 10),
                                       bg='white', fg='black')
        self.dl_data_info_label.pack(side=LEFT, padx=10)

    def select_data_directory(self):
        """选择数据目录"""
        directory = filedialog.askdirectory(title="选择包含故障数据的目录")
        if directory:
            self.dl_data_dir_var.set(directory)
            self.update_directory_tree(directory)

    def update_directory_tree(self, directory):
        """更新目录结构显示"""
        # 清空现有显示
        for widget in self.dir_tree_frame.winfo_children():
            widget.destroy()

        try:
            # 创建目录树显示
            tree_label = Label(self.dir_tree_frame, text="目录结构:",
                             font=("微软雅黑", 10, "bold"),
                             bg='white', fg='black')
            tree_label.pack(anchor=W, pady=(0, 5))

            # 创建滚动文本框显示目录结构
            tree_text = scrolledtext.ScrolledText(self.dir_tree_frame, height=8, width=80,
                                                 font=("Consolas", 9),
                                                 bg='white', fg='black')
            tree_text.pack(fill=BOTH, expand=True)

            # 遍历目录结构
            tree_content = f"📁 {os.path.basename(directory)}\n"
            subdirs = []
            files = []

            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                if os.path.isdir(item_path):
                    subdirs.append(item)
                else:
                    files.append(item)

            # 显示子目录
            for subdir in sorted(subdirs):
                tree_content += f"├── 📁 {subdir}\n"
                subdir_path = os.path.join(directory, subdir)
                try:
                    subfiles = [f for f in os.listdir(subdir_path) if f.endswith(('.mat', '.xlsx', '.csv'))]
                    for i, subfile in enumerate(sorted(subfiles[:5])):  # 只显示前5个文件
                        prefix = "│   ├── " if i < len(subfiles) - 1 else "│   └── "
                        tree_content += f"{prefix}📄 {subfile}\n"
                    if len(subfiles) > 5:
                        tree_content += f"│   └── ... 还有 {len(subfiles) - 5} 个文件\n"
                except:
                    tree_content += f"│   └── (无法访问)\n"

            # 显示文件
            data_files = [f for f in sorted(files) if f.endswith(('.mat', '.xlsx', '.csv'))]
            for file in data_files[:10]:  # 只显示前10个文件
                tree_content += f"├── 📄 {file}\n"
            if len(data_files) > 10:
                tree_content += f"└── ... 还有 {len(data_files) - 10} 个文件\n"

            tree_text.insert(END, tree_content)
            tree_text.config(state='disabled')

        except Exception as e:
            error_label = Label(self.dir_tree_frame, text=f"无法读取目录: {str(e)}",
                              font=("微软雅黑", 10),
                              bg='white', fg=self.colors['error'])
            error_label.pack(anchor=W)

    def load_dl_data(self):
        """加载深度学习数据"""
        if not DL_AVAILABLE:
            messagebox.showerror("功能不可用", "深度学习库未正确加载")
            return

        data_dir = self.dl_data_dir_var.get().strip()
        if not data_dir or not os.path.exists(data_dir):
            messagebox.showerror("错误", "请先选择有效的数据目录")
            return

        try:
            # 更新状态
            self.dl_status_var.set("正在加载数据...")
            self.dl_data_info_label.config(text="正在处理数据文件...")

            # 获取参数
            sample_length = int(self.dl_sample_length_var.get())
            stride = int(self.dl_stride_var.get())
            samples_per_class = int(self.dl_samples_per_class_var.get())

            # 在新线程中加载数据
            import threading
            thread = threading.Thread(target=self._load_data_worker,
                                     args=(data_dir, sample_length, stride, samples_per_class))
            thread.daemon = True
            thread.start()

        except ValueError as e:
            messagebox.showerror("参数错误", f"请检查输入参数: {str(e)}")
        except Exception as e:
            messagebox.showerror("加载错误", f"数据加载失败: {str(e)}")
            self.dl_status_var.set("数据加载失败")

    def _load_data_worker(self, data_dir, sample_length, stride, samples_per_class):
        """数据加载工作线程"""
        try:
            # 这里实现数据加载逻辑
            # 扫描目录，识别故障类型，加载数据文件
            fault_types = []
            data_dict = {}

            # 扫描子目录，每个子目录代表一种故障类型
            print(f"扫描数据目录: {data_dir}")
            for item in os.listdir(data_dir):
                item_path = os.path.join(data_dir, item)
                if os.path.isdir(item_path):
                    print(f"发现故障类型目录: {item}")
                    fault_types.append(item)
                    data_dict[item] = []

                    # 加载该故障类型的数据文件
                    file_count = 0
                    for file in os.listdir(item_path):
                        if file.endswith(('.mat', '.xlsx', '.csv')):
                            file_path = os.path.join(item_path, file)
                            print(f"正在加载文件: {file_path}")
                            try:
                                data = None
                                if file.endswith('.mat'):
                                    data = self._load_mat_file(file_path)
                                elif file.endswith('.xlsx'):
                                    data = self._load_excel_file(file_path)
                                elif file.endswith('.csv'):
                                    data = self._load_csv_file(file_path)

                                if data is not None:
                                    print(f"文件加载成功: {file}, 数据长度: {len(data)}")
                                    if len(data) > sample_length:
                                        data_dict[item].append(data)
                                        file_count += 1
                                    else:
                                        print(f"数据长度不足: {len(data)} <= {sample_length}")
                                else:
                                    print(f"文件加载失败: {file}")
                            except Exception as e:
                                print(f"加载文件异常 {file_path}: {e}")
                                import traceback
                                traceback.print_exc()

                    print(f"故障类型 {item} 成功加载 {file_count} 个文件")

            # 更新UI状态
            self.master.after(0, self._update_data_loading_status,
                            f"发现 {len(fault_types)} 种故障类型，正在生成样本...")

            # 生成样本
            samples_data = []
            samples_labels = []

            for label_idx, fault_type in enumerate(fault_types):
                if fault_type in data_dict and data_dict[fault_type]:
                    samples_count = 0
                    for data in data_dict[fault_type]:
                        if samples_count >= samples_per_class:
                            break

                        # 滑动窗口生成样本
                        for i in range(0, len(data) - sample_length + 1, stride):
                            if samples_count >= samples_per_class:
                                break
                            sample = data[i:i + sample_length]
                            samples_data.append(sample)
                            samples_labels.append(label_idx)
                            samples_count += 1

            # 检查是否有有效样本
            if not samples_data or not samples_labels:
                error_msg = f"未能生成有效样本。请检查:\n1. 数据文件是否存在\n2. 数据长度是否足够(需要>{sample_length})\n3. 文件格式是否正确"
                self.master.after(0, self._update_data_loading_error, error_msg)
                return

            # 转换为numpy数组
            if DL_AVAILABLE:
                import numpy as np
                X = np.array(samples_data)
                y = np.array(samples_labels)

                print(f"生成样本统计: 总样本数={len(X)}, 样本维度={X.shape}, 标签数={len(y)}")
                print(f"各类样本数: {[np.sum(y == i) for i in range(len(fault_types))]}")

                # 保存数据
                self.dl_raw_data = (X, y, fault_types)
                self.dl_data_loaded = True

                # 更新UI
                info_text = f"数据加载完成！\n故障类型: {fault_types}\n总样本数: {len(X)}\n样本维度: {X.shape}"
                self.master.after(0, self._update_data_loading_complete, info_text)
            else:
                error_msg = "深度学习库不可用，无法处理数据"
                self.master.after(0, self._update_data_loading_error, error_msg)

        except Exception as e:
            error_msg = f"数据加载失败: {str(e)}"
            self.master.after(0, self._update_data_loading_error, error_msg)

    def _load_mat_file(self, file_path):
        """加载MAT文件 - 支持CWRU和通用格式"""
        try:
            import scipy.io as sio
            import numpy as np
            mat_data = sio.loadmat(file_path)

            print(f"加载MAT文件: {file_path}")
            print(f"MAT文件包含的键: {list(mat_data.keys())}")

            # 优先查找CWRU格式的DE_time数据（Drive End时域数据）
            de_time_keys = [key for key in mat_data.keys() if 'DE_time' in key and not key.startswith('__')]
            if de_time_keys:
                key = de_time_keys[0]
                value = mat_data[key]
                print(f"找到CWRU DE_time数据: {key}, shape={value.shape}, dtype={value.dtype}")
                if isinstance(value, np.ndarray) and value.size > 0:
                    return value.flatten()

            # 查找vibration_data字段（测试数据格式）
            if 'vibration_data' in mat_data:
                value = mat_data['vibration_data']
                print(f"找到vibration_data: shape={value.shape}, dtype={value.dtype}")
                if isinstance(value, np.ndarray) and value.size > 0:
                    return value.flatten()

            # 通用数据字段查找
            for key, value in mat_data.items():
                if not key.startswith('__') and isinstance(value, np.ndarray) and value.size > 0:
                    print(f"找到数据字段 {key}: shape={value.shape}, dtype={value.dtype}")

                    # 跳过RPM等非振动数据
                    if 'RPM' in key.upper() or 'rpm' in key:
                        continue

                    if value.ndim == 1:
                        return value.flatten()
                    elif value.ndim == 2:
                        # 如果是2D数组，取第一列或展平
                        if min(value.shape) == 1:
                            return value.flatten()
                        else:
                            # 取第一列
                            return value[:, 0].flatten()
                    elif value.ndim > 2:
                        # 多维数组，展平
                        return value.flatten()

            print(f"MAT文件中未找到有效的振动数据字段")
            return None
        except Exception as e:
            print(f"MAT文件加载失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _load_excel_file(self, file_path):
        """加载Excel文件"""
        try:
            df = pd.read_excel(file_path)
            # 假设第一列是振动数据
            if len(df.columns) > 0:
                return df.iloc[:, 0].values
            return None
        except Exception as e:
            print(f"Excel文件加载失败: {e}")
            return None

    def _load_csv_file(self, file_path):
        """加载CSV文件"""
        try:
            df = pd.read_csv(file_path)
            # 假设第一列是振动数据
            if len(df.columns) > 0:
                return df.iloc[:, 0].values
            return None
        except Exception as e:
            print(f"CSV文件加载失败: {e}")
            return None

    def _update_data_loading_status(self, message):
        """更新数据加载状态"""
        self.dl_status_var.set(message)
        self.dl_data_info_label.config(text=message)

    def _update_data_loading_complete(self, info_text):
        """数据加载完成"""
        self.dl_status_var.set("数据加载完成")
        self.dl_data_info_label.config(text=info_text, fg=self.colors['success'])

    def _update_data_loading_error(self, error_msg):
        """数据加载错误"""
        self.dl_status_var.set("数据加载失败")
        self.dl_data_info_label.config(text=error_msg, fg=self.colors['error'])
        messagebox.showerror("数据加载失败", error_msg)

    def create_data_preprocessing_tab(self):
        """创建数据预处理选项卡"""
        # 创建选项卡框架
        preprocess_frame = Frame(self.dl_notebook, bg='white')
        self.dl_notebook.add(preprocess_frame, text="🔧 数据预处理")

        # === 数据集划分设置 ===
        split_section = LabelFrame(preprocess_frame, text="📊 数据集划分",
                                  font=("微软雅黑", 10, "bold"),  # 减小字体
                                  bg='white', fg='black')
        split_section.pack(fill=X, padx=20, pady=(10, 5))  # 减小垂直间距

        split_frame = Frame(split_section, bg='white')
        split_frame.pack(fill=X, padx=15, pady=8)  # 减小内边距

        # 三列布局
        train_col = Frame(split_frame, bg='white')
        train_col.pack(side=LEFT, fill=X, expand=True, padx=(0, 20))

        valid_col = Frame(split_frame, bg='white')
        valid_col.pack(side=LEFT, fill=X, expand=True, padx=(0, 20))

        test_col = Frame(split_frame, bg='white')
        test_col.pack(side=LEFT, fill=X, expand=True)

        # 训练集比例
        Label(train_col, text="训练集比例:", font=("微软雅黑", 9, "bold"),  # 减小字体
              bg='white', fg='black').pack(anchor=W)
        Entry(train_col, textvariable=self.dl_train_ratio_var,
              font=("微软雅黑", 9), width=12, bg='white', fg='black').pack(anchor=W, pady=(1, 0))  # 减小宽度和间距

        # 验证集比例
        Label(valid_col, text="验证集比例:", font=("微软雅黑", 9, "bold"),  # 减小字体
              bg='white', fg='black').pack(anchor=W)
        Entry(valid_col, textvariable=self.dl_valid_ratio_var,
              font=("微软雅黑", 9), width=12, bg='white', fg='black').pack(anchor=W, pady=(1, 0))  # 减小宽度和间距

        # 测试集比例
        Label(test_col, text="测试集比例:", font=("微软雅黑", 9, "bold"),  # 减小字体
              bg='white', fg='black').pack(anchor=W)
        Entry(test_col, textvariable=self.dl_test_ratio_var,
              font=("微软雅黑", 9), width=12, bg='white', fg='black').pack(anchor=W, pady=(1, 0))  # 减小宽度和间距

        # === 预处理控制 ===
        control_section = LabelFrame(preprocess_frame, text="🎛️ 预处理控制",
                                    font=("微软雅黑", 10, "bold"),  # 减小字体
                                    bg='white', fg='black')
        control_section.pack(fill=X, padx=20, pady=(5, 10))  # 减小垂直间距

        control_frame = Frame(control_section, bg='white')
        control_frame.pack(fill=X, padx=15, pady=8)  # 减小内边距

        # 预处理按钮
        Button(control_frame, text="🔧 开始预处理", font=("微软雅黑", 10, "bold"),  # 减小字体
               bg=self.colors['primary'], fg='white', relief='flat',
               padx=15, pady=5, command=self.preprocess_dl_data).pack(side=LEFT, padx=(0, 10))  # 减小按钮尺寸

        # 预处理状态显示
        self.dl_preprocess_info_label = Label(control_frame, text="未开始预处理",
                                             font=("微软雅黑", 9),  # 减小字体
                                             bg='white', fg='black')
        self.dl_preprocess_info_label.pack(side=LEFT, padx=10)

        # === 数据可视化 ===
        viz_section = LabelFrame(preprocess_frame, text="📈 数据可视化",
                                font=("微软雅黑", 12, "bold"),
                                bg='white', fg='black')
        viz_section.pack(fill=BOTH, expand=True, padx=20, pady=20)

        viz_frame = Frame(viz_section, bg='white')
        viz_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)

        # 可视化按钮
        viz_buttons_frame = Frame(viz_frame, bg='white')
        viz_buttons_frame.pack(fill=X, pady=(0, 10))

        Button(viz_buttons_frame, text="📈 样本波形", font=("微软雅黑", 10),
               bg=self.colors['accent'], fg='white', relief='flat',
               command=self.show_sample_waveforms).pack(side=LEFT, padx=(0, 10))

        # 可视化显示区域 - 扩大显示区域
        self.dl_viz_frame = Frame(viz_frame, bg='white', relief='sunken', bd=1)
        self.dl_viz_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

    def create_model_settings_tab(self):
        """创建模型设置选项卡"""
        # 创建选项卡框架
        model_frame = Frame(self.dl_notebook, bg='white')
        self.dl_notebook.add(model_frame, text="⚙️ 模型设置")

        # === 训练参数设置 ===
        params_section = LabelFrame(model_frame, text="🎯 训练参数",
                                   font=("微软雅黑", 12, "bold"),
                                   bg='white', fg='black')
        params_section.pack(fill=X, padx=20, pady=20)

        params_frame = Frame(params_section, bg='white')
        params_frame.pack(fill=X, padx=15, pady=15)

        # 两列布局
        left_params = Frame(params_frame, bg='white')
        left_params.pack(side=LEFT, fill=X, expand=True, padx=(0, 30))

        right_params = Frame(params_frame, bg='white')
        right_params.pack(side=LEFT, fill=X, expand=True)

        # 左列参数
        Label(left_params, text="批次大小:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)
        Entry(left_params, textvariable=self.dl_batch_size_var,
              font=("微软雅黑", 10), width=20, bg='white', fg='black').pack(anchor=W, pady=(2, 15))

        Label(left_params, text="学习率:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)
        Entry(left_params, textvariable=self.dl_learning_rate_var,
              font=("微软雅黑", 10), width=20, bg='white', fg='black').pack(anchor=W, pady=(2, 15))

        # 右列参数
        Label(right_params, text="训练轮次:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)
        Entry(right_params, textvariable=self.dl_epochs_var,
              font=("微软雅黑", 10), width=20, bg='white', fg='black').pack(anchor=W, pady=(2, 15))

        Label(right_params, text="Dropout率:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)
        Entry(right_params, textvariable=self.dl_dropout_var,
              font=("微软雅黑", 10), width=20, bg='white', fg='black').pack(anchor=W, pady=(2, 15))

        # === 模型架构信息 ===
        arch_section = LabelFrame(model_frame, text="🏗️ 模型架构",
                                 font=("微软雅黑", 12, "bold"),
                                 bg='white', fg='black')
        arch_section.pack(fill=BOTH, expand=True, padx=20, pady=20)

        arch_text = scrolledtext.ScrolledText(arch_section, height=15, width=80,
                                             font=("Consolas", 9),
                                             bg='white', fg='black')
        arch_text.pack(fill=BOTH, expand=True, padx=15, pady=15)

        # 显示1DCNN架构信息
        arch_info = """
1D CNN 故障诊断模型架构:

输入层:
  - 输入维度: (batch_size, sample_length, 1)
  - 数据类型: 一维振动信号

卷积层1:
  - 卷积核数量: 32
  - 卷积核大小: 64
  - 激活函数: ReLU
  - 池化: MaxPooling1D(pool_size=2)

卷积层2:
  - 卷积核数量: 64
  - 卷积核大小: 32
  - 激活函数: ReLU
  - 池化: MaxPooling1D(pool_size=2)

卷积层3:
  - 卷积核数量: 128
  - 卷积核大小: 16
  - 激活函数: ReLU
  - 池化: MaxPooling1D(pool_size=2)

全连接层:
  - Flatten层
  - Dense层: 256个神经元, ReLU激活
  - Dropout层: 防止过拟合
  - Dense层: 128个神经元, ReLU激活
  - 输出层: num_classes个神经元, Softmax激活

优化器: Adam
损失函数: Categorical Crossentropy
评估指标: Accuracy
        """

        arch_text.insert(END, arch_info)
        arch_text.config(state='disabled')

    def create_model_training_tab(self):
        """创建模型训练选项卡"""
        # 创建选项卡框架
        training_frame = Frame(self.dl_notebook, bg='white')
        self.dl_notebook.add(training_frame, text="🚀 模型训练")

        # === 训练控制 ===
        control_section = LabelFrame(training_frame, text="🎛️ 训练控制",
                                    font=("微软雅黑", 12, "bold"),
                                    bg='white', fg='black')
        control_section.pack(fill=X, padx=20, pady=20)

        control_frame = Frame(control_section, bg='white')
        control_frame.pack(fill=X, padx=15, pady=15)

        # 训练按钮
        Button(control_frame, text="🚀 开始训练", font=("微软雅黑", 11, "bold"),
               bg=self.colors['success'], fg='white', relief='flat',
               padx=20, pady=8, command=self.start_dl_training).pack(side=LEFT, padx=(0, 10))

        Button(control_frame, text="⏹️ 停止训练", font=("微软雅黑", 11, "bold"),
               bg=self.colors['error'], fg='white', relief='flat',
               padx=20, pady=8, command=self.stop_dl_training).pack(side=LEFT, padx=(0, 10))

        # 模型保存/加载
        Button(control_frame, text="💾 保存模型", font=("微软雅黑", 10),
               bg=self.colors['primary'], fg='white', relief='flat',
               command=self.save_dl_model).pack(side=LEFT, padx=(0, 10))

        Button(control_frame, text="📂 加载模型", font=("微软雅黑", 10),
               bg=self.colors['primary'], fg='white', relief='flat',
               command=self.load_dl_model).pack(side=LEFT, padx=(0, 10))

        # === 训练进度显示 ===
        progress_section = LabelFrame(training_frame, text="📊 训练进度",
                                     font=("微软雅黑", 12, "bold"),
                                     bg='white', fg='black')
        progress_section.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # 训练状态和进度条
        status_frame = Frame(progress_section, bg='white')
        status_frame.pack(fill=X, padx=15, pady=10)

        self.dl_training_status_label = Label(status_frame, text="未开始训练",
                                             font=("微软雅黑", 10, "bold"),
                                             bg='white', fg='black')
        self.dl_training_status_label.pack(anchor=W)

        self.dl_training_progress = ttk.Progressbar(status_frame, length=400, mode='determinate')
        self.dl_training_progress.pack(fill=X, pady=5)

        # 训练信息标签
        self.dl_train_info_label = Label(status_frame, text="未开始训练",
                                        font=("微软雅黑", 10), bg='white', fg='black')
        self.dl_train_info_label.pack(anchor=W, pady=5)

        # 训练曲线显示区域
        self.dl_train_viz_frame = Frame(progress_section, bg='white', relief='sunken', bd=1)
        self.dl_train_viz_frame.pack(fill=BOTH, expand=True, padx=15, pady=10)

    def create_model_evaluation_tab(self):
        """创建模型评估选项卡"""
        # 创建选项卡框架
        eval_frame = Frame(self.dl_notebook, bg='white')
        self.dl_notebook.add(eval_frame, text="📈 模型评估")

        # === 评估控制 ===
        control_section = LabelFrame(eval_frame, text="🎛️ 评估控制",
                                    font=("微软雅黑", 12, "bold"),
                                    bg='white', fg='black')
        control_section.pack(fill=X, padx=20, pady=20)

        control_frame = Frame(control_section, bg='white')
        control_frame.pack(fill=X, padx=15, pady=15)

        # 评估按钮
        Button(control_frame, text="📊 模型评估", font=("微软雅黑", 11, "bold"),
               bg=self.colors['primary'], fg='white', relief='flat',
               padx=20, pady=8, command=self.evaluate_dl_model).pack(side=LEFT, padx=(0, 10))

        Button(control_frame, text="🎯 混淆矩阵", font=("微软雅黑", 10),
               bg=self.colors['accent'], fg='white', relief='flat',
               command=self.show_confusion_matrix).pack(side=LEFT, padx=(0, 10))

        Button(control_frame, text="🔍 t-SNE可视化", font=("微软雅黑", 10),
               bg=self.colors['accent'], fg='white', relief='flat',
               command=self.show_tsne_visualization).pack(side=LEFT, padx=(0, 10))

        # === 评估结果显示 ===
        results_section = LabelFrame(eval_frame, text="📋 评估结果",
                                     font=("微软雅黑", 12, "bold"),
                                     bg='white', fg='black')
        results_section.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # 评估信息标签
        self.dl_eval_info_label = Label(results_section, text="未开始评估",
                                       font=("微软雅黑", 10), bg='white', fg='black')
        self.dl_eval_info_label.pack(anchor=W, padx=15, pady=5)

        # 评估结果显示区域
        self.dl_eval_results_frame = Frame(results_section, bg='white', relief='sunken', bd=1)
        self.dl_eval_results_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)

    def create_fault_prediction_tab(self):
        """创建故障预测选项卡"""
        # 创建选项卡框架
        predict_frame = Frame(self.dl_notebook, bg='white')
        self.dl_notebook.add(predict_frame, text="🔮 故障预测")

        # === 预测控制 ===
        control_section = LabelFrame(predict_frame, text="🎛️ 预测控制",
                                    font=("微软雅黑", 12, "bold"),
                                    bg='white', fg='black')
        control_section.pack(fill=X, padx=20, pady=20)

        control_frame = Frame(control_section, bg='white')
        control_frame.pack(fill=X, padx=15, pady=15)

        # 数据选择
        Label(control_frame, text="选择预测数据:", font=("微软雅黑", 10, "bold"),
              bg='white', fg='black').pack(anchor=W)

        # 第一行：文件选择按钮
        data_select_frame1 = Frame(control_frame, bg='white')
        data_select_frame1.pack(fill=X, pady=5)

        Button(data_select_frame1, text="📁 选择文件", font=("微软雅黑", 10),
               bg=self.colors['primary'], fg='white', relief='flat',
               command=self.select_prediction_file).pack(side=LEFT, padx=(0, 10))

        Button(data_select_frame1, text="📎 选择附件", font=("微软雅黑", 10),
               bg=self.colors['accent'], fg='white', relief='flat',
               command=self.select_attachment_for_prediction).pack(side=LEFT, padx=(0, 10))

        Button(data_select_frame1, text="🗑️ 删除选择", font=("微软雅黑", 10),
               bg=self.colors['error'], fg='white', relief='flat',
               command=self.clear_prediction_file).pack(side=LEFT, padx=(0, 10))

        # 第二行：处理和预测按钮
        data_select_frame2 = Frame(control_frame, bg='white')
        data_select_frame2.pack(fill=X, pady=5)

        Button(data_select_frame2, text="🔧 数据预处理", font=("微软雅黑", 10),
               bg=self.colors['warning'], fg='white', relief='flat',
               command=self.preprocess_prediction_data).pack(side=LEFT, padx=(0, 10))

        Button(data_select_frame2, text="🔮 开始预测", font=("微软雅黑", 10, "bold"),
               bg=self.colors['success'], fg='white', relief='flat',
               padx=20, pady=8, command=self.start_dl_prediction).pack(side=LEFT, padx=(0, 10))

        # === 预测结果显示 ===
        results_section = LabelFrame(predict_frame, text="📊 预测结果",
                                     font=("微软雅黑", 12, "bold"),
                                     bg='white', fg='black')
        results_section.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # 预测信息标签
        self.dl_predict_info_label = Label(results_section, text="请选择预测文件",
                                          font=("微软雅黑", 10), bg='white', fg='black')
        self.dl_predict_info_label.pack(anchor=W, padx=15, pady=5)

        # 预测结果显示区域
        self.dl_predict_results_frame = Frame(results_section, bg='white', relief='sunken', bd=1)
        self.dl_predict_results_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)

    # ============== 深度学习功能实现方法 ==============

    def preprocess_dl_data(self):
        """预处理深度学习数据"""
        if not self.dl_data_loaded or not hasattr(self, 'dl_raw_data'):
            messagebox.showerror("错误", "请先加载数据")
            return

        try:
            X, y, fault_types = self.dl_raw_data

            # 检查数据是否为空
            if X is None:
                messagebox.showerror("数据错误", "加载的数据为空，请检查数据文件")
                self.dl_preprocess_info_label.config(text="数据为空，预处理失败", fg='red')
                return

            if y is None:
                messagebox.showerror("数据错误", "标签数据为空，请检查数据文件")
                self.dl_preprocess_info_label.config(text="标签为空，预处理失败", fg='red')
                return

            # 确保numpy可用
            import numpy as np

            # 转换为numpy数组进行检查
            X_array = np.array(X) if not isinstance(X, np.ndarray) else X
            y_array = np.array(y) if not isinstance(y, np.ndarray) else y

            if X_array.size == 0:
                messagebox.showerror("数据错误", "数据数组为空")
                self.dl_preprocess_info_label.config(text="数据数组为空，预处理失败", fg='red')
                return

            if y_array.size == 0:
                messagebox.showerror("数据错误", "标签数组为空")
                self.dl_preprocess_info_label.config(text="标签数组为空，预处理失败", fg='red')
                return

            # 安全地检查数据形状
            try:
                X_shape = X_array.shape
                y_shape = y_array.shape
                print(f"数据检查: X.shape={X_shape}, y.shape={y_shape}")
                print(f"故障类型: {fault_types}")
            except Exception as e:
                print(f"数据形状检查失败: {e}")

            # 获取数据集划分比例
            try:
                train_ratio = float(self.dl_train_ratio_var.get())
                valid_ratio = float(self.dl_valid_ratio_var.get())
                test_ratio = float(self.dl_test_ratio_var.get())
            except ValueError as e:
                messagebox.showerror("参数错误", f"比例参数格式错误: {e}")
                return

            # 检查比例是否合理
            if abs(train_ratio + valid_ratio + test_ratio - 1.0) > 0.01:
                messagebox.showerror("参数错误", "训练集、验证集、测试集比例之和应为1.0")
                return

            self.dl_preprocess_info_label.config(text="正在预处理数据...", fg='blue')

            # 数据标准化
            if DL_AVAILABLE:
                # 导入必要的库
                import numpy as np
                from sklearn.preprocessing import StandardScaler
                from sklearn.model_selection import train_test_split

                # 使用PyTorch进行深度学习预处理
                try:
                    import torch
                except ImportError:
                    raise ImportError("PyTorch未安装，请安装: pip install torch torchvision")

                # 检查数据维度
                print(f"预处理前数据维度: X.shape={X_array.shape}, y.shape={y_array.shape}")

                if X_array.size == 0 or y_array.size == 0:
                    raise ValueError("数据为空，无法进行预处理")

                # 重塑数据为2D用于标准化
                original_shape = X_array.shape
                X_reshaped = X_array.reshape(X_array.shape[0], -1)
                print(f"重塑后数据维度: X_reshaped.shape={X_reshaped.shape}")

                # Z-score标准化 (零均值单位方差)
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X_reshaped)
                print(f"Z-score标准化后数据维度: X_scaled.shape={X_scaled.shape}")

                # 验证Z-score标准化效果
                mean_after = np.mean(X_scaled, axis=0)
                std_after = np.std(X_scaled, axis=0)
                print(f"Z-score标准化验证: 均值={np.mean(mean_after):.6f}, 标准差={np.mean(std_after):.6f}")

                # 重塑回原始形状
                X_scaled = X_scaled.reshape(original_shape)
                print(f"重塑回原始形状: X_scaled.shape={X_scaled.shape}")

                # 添加通道维度 (samples, length, channels)
                X_scaled = X_scaled.reshape(X_scaled.shape[0], X_scaled.shape[1], 1)
                print(f"添加通道维度后: X_scaled.shape={X_scaled.shape}")

                # PyTorch标签编码 - 使用LongTensor
                y_processed = y_array.astype(np.int64)

                # 数据集划分
                X_temp, X_test, y_temp, y_test = train_test_split(
                    X_scaled, y_processed, test_size=test_ratio,
                    stratify=y_processed, random_state=42
                )

                valid_ratio_adjusted = valid_ratio / (train_ratio + valid_ratio)
                X_train, X_valid, y_train, y_valid = train_test_split(
                    X_temp, y_temp, test_size=valid_ratio_adjusted,
                    stratify=y_temp, random_state=42
                )

                # 保存预处理后的数据
                self.dl_train_data = (X_train, y_train)
                self.dl_valid_data = (X_valid, y_valid)
                self.dl_test_data = (X_test, y_test)
                self.dl_scaler = scaler
                self.dl_fault_types = fault_types

                # 更新状态
                info_text = f"预处理完成！\n训练集: {X_train.shape[0]} 样本\n验证集: {X_valid.shape[0]} 样本\n测试集: {X_test.shape[0]} 样本"
                self.dl_preprocess_info_label.config(text=info_text, fg=self.colors['success'])

        except Exception as e:
            error_msg = f"数据预处理失败: {str(e)}"
            self.dl_preprocess_info_label.config(text=error_msg, fg=self.colors['error'])
            messagebox.showerror("预处理失败", error_msg)



    def show_sample_waveforms(self):
        """显示样本波形"""
        if not hasattr(self, 'dl_raw_data'):
            messagebox.showwarning("提示", "请先加载数据")
            return

        try:
            # 确保numpy可用
            import numpy as np

            X, y, fault_types = self.dl_raw_data

            # 清空显示区域
            for widget in self.dl_viz_frame.winfo_children():
                widget.destroy()

            # 创建matplotlib图形 - 优化尺寸和布局
            num_types = len(fault_types)
            # 减小图形高度，每个子图高度从4减少到2.5
            fig, axes = plt.subplots(num_types, 1, figsize=(12, 2.5*num_types))
            if num_types == 1:
                axes = [axes]

            # 故障类型中文映射
            fault_type_chinese = {
                # 轴承故障类型
                'normal': '正常状态',
                'ball_fault': '滚动体故障',
                'inner_race_fault': '内圈故障',
                'outer_race_fault': '外圈故障',
                'IR': '内圈故障',
                'OR': '外圈故障',
                'B': '滚动体故障',
                'N': '正常状态',

                # 风机故障类型
                'rotor_unbalance': '转子不平衡',
                'rotor_misalignment': '转子不对中',
                'blade_fault': '叶片故障',
                'RU': '转子不平衡',
                'RM': '转子不对中',
                'BF': '叶片故障'
            }

            # 为每种故障类型显示一个样本波形
            y_array = np.array(y) if not isinstance(y, np.ndarray) else y
            X_array = np.array(X) if not isinstance(X, np.ndarray) else X

            for i, fault_type in enumerate(fault_types):
                # 找到该故障类型的第一个样本
                indices = np.where(y_array == i)[0]
                if len(indices) > 0:
                    sample = X_array[indices[0]]
                    axes[i].plot(sample, color=f'C{i}', linewidth=0.8)

                    # 使用中文标题 - 减小字体
                    chinese_name = fault_type_chinese.get(fault_type, fault_type)
                    axes[i].set_title(f'{chinese_name} - 典型样本波形',
                                    fontsize=10, fontweight='bold',
                                    fontfamily='Microsoft YaHei')
                    axes[i].set_xlabel('采样点', fontsize=8, fontfamily='Microsoft YaHei')
                    axes[i].set_ylabel('振动幅值', fontsize=8, fontfamily='Microsoft YaHei')
                    axes[i].grid(True, alpha=0.3)

                    # 减小刻度标签字体
                    axes[i].tick_params(labelsize=7)

                    # 添加统计信息 - 减小字体和框大小
                    rms = np.sqrt(np.mean(sample**2))
                    peak = np.max(np.abs(sample))
                    axes[i].text(0.02, 0.98, f'RMS: {rms:.4f}\n峰值: {peak:.4f}',
                               transform=axes[i].transAxes, verticalalignment='top',
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                               fontsize=7, fontfamily='Microsoft YaHei')

            # 调整子图间距，使布局更紧凑，并为y轴标题预留空间
            plt.tight_layout(pad=1.0, h_pad=0.5, rect=[0.08, 0, 1, 1])  # 左侧预留8%空间给y轴标题

            # 统一y轴标题位置
            for ax in axes:
                ax.yaxis.set_label_coords(-0.08, 0.5)

            # 嵌入到tkinter
            canvas = FigureCanvasTkAgg(fig, self.dl_viz_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=BOTH, expand=True)

        except Exception as e:
            messagebox.showerror("可视化失败", f"显示样本波形失败: {str(e)}")

    # ============== 深度学习模型相关方法 ==============

    def start_dl_training(self):
        """开始深度学习训练"""
        if not hasattr(self, 'dl_train_data') or self.dl_train_data is None:
            messagebox.showerror("错误", "请先完成数据预处理")
            return

        # 检查是否正在训练
        if hasattr(self, 'dl_training') and self.dl_training:
            messagebox.showwarning("提示", "模型正在训练中，请等待完成或先停止训练")
            return

        # 启动训练线程
        self.dl_training = True
        self.dl_training_thread = threading.Thread(target=self._train_model_thread, daemon=True)
        self.dl_training_thread.start()

        # 更新UI状态
        self.dl_train_info_label.config(text="正在初始化训练...", fg=self.colors['primary'])

    def _train_model_thread(self):
        """模型训练线程"""
        try:
            # 导入深度学习模型
            from dl_models import CNN1D, VibrationDataset, ModelTrainer, plot_training_history
            import torch
            from torch.utils.data import DataLoader

            # 获取预处理数据
            X_train, y_train = self.dl_train_data
            X_valid, y_valid = self.dl_valid_data
            X_test, y_test = self.dl_test_data
            fault_types = self.dl_fault_types

            # 更新状态
            self.master.after(0, lambda: self.dl_train_info_label.config(
                text="正在创建数据加载器...", fg=self.colors['primary']))

            # 创建数据集和数据加载器
            train_dataset = VibrationDataset(X_train, y_train)
            valid_dataset = VibrationDataset(X_valid, y_valid)
            test_dataset = VibrationDataset(X_test, y_test)

            # 获取训练参数
            batch_size = int(self.dl_batch_size_var.get())
            learning_rate = float(self.dl_learning_rate_var.get())
            epochs = int(self.dl_epochs_var.get())
            dropout_rate = float(self.dl_dropout_var.get())

            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            valid_loader = DataLoader(valid_dataset, batch_size=batch_size, shuffle=False)
            test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

            # 创建模型
            self.master.after(0, lambda: self.dl_train_info_label.config(
                text="正在创建模型...", fg=self.colors['primary']))

            input_length = X_train.shape[1]
            num_classes = len(fault_types)
            device = 'cuda' if torch.cuda.is_available() else 'cpu'

            model = CNN1D(input_length=input_length, num_classes=num_classes, dropout_rate=dropout_rate)
            trainer = ModelTrainer(model, device=device)

            # 定义进度回调函数 - 支持实时曲线更新
            def progress_callback(epoch, total_epochs, train_loss, train_acc, val_loss, val_acc):
                if not self.dl_training:  # 检查是否被停止
                    return

                progress = (epoch / total_epochs) * 100
                info_text = (f"训练进度: {epoch}/{total_epochs} ({progress:.1f}%)\n"
                           f"训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.2f}%\n"
                           f"验证损失: {val_loss:.4f}, 验证准确率: {val_acc:.2f}%")

                self.master.after(0, lambda: self.dl_train_info_label.config(
                    text=info_text, fg=self.colors['primary']))

                # 更新进度条
                if hasattr(self, 'dl_training_progress'):
                    self.master.after(0, lambda: self.dl_training_progress.config(value=progress))

                # 实时更新训练曲线 - 每5个epoch更新一次以避免过于频繁
                if epoch % 5 == 0 or epoch == total_epochs:
                    self.master.after(0, lambda: self._plot_training_curves(real_time=True))

            # 开始训练
            self.master.after(0, lambda: self.dl_train_info_label.config(
                text=f"开始训练模型 (设备: {device})...", fg=self.colors['primary']))

            history = trainer.train(
                train_loader=train_loader,
                val_loader=valid_loader,
                epochs=epochs,
                learning_rate=learning_rate,
                progress_callback=progress_callback
            )

            if not self.dl_training:  # 检查是否被停止
                return

            # 保存模型和训练历史
            self.dl_model = model
            self.dl_trainer = trainer
            self.dl_training_history = history
            self.dl_test_loader = test_loader

            # 训练完成
            best_acc = history['best_val_accuracy']
            final_text = (f"训练完成！\n"
                         f"最佳验证准确率: {best_acc:.2f}%\n"
                         f"模型已保存")

            self.master.after(0, lambda: self.dl_train_info_label.config(
                text=final_text, fg=self.colors['success']))

            # 绘制训练曲线
            self.master.after(0, self._plot_training_curves)

        except Exception as e:
            error_msg = f"训练失败: {str(e)}"
            self.master.after(0, lambda: self.dl_train_info_label.config(
                text=error_msg, fg=self.colors['error']))
            self.master.after(0, lambda: messagebox.showerror("训练失败", error_msg))
        finally:
            self.dl_training = False

    def stop_dl_training(self):
        """停止深度学习训练"""
        if hasattr(self, 'dl_training') and self.dl_training:
            self.dl_training = False
            self.dl_train_info_label.config(text="正在停止训练...", fg=self.colors['warning'])
            messagebox.showinfo("提示", "训练停止指令已发送")
        else:
            messagebox.showinfo("提示", "当前没有正在进行的训练")

    def _plot_training_curves(self, real_time=False):
        """绘制训练曲线 - 支持实时更新"""
        if not hasattr(self, 'dl_training_history'):
            return

        try:
            # 如果不是实时更新，清空显示区域
            if not real_time:
                for widget in self.dl_train_viz_frame.winfo_children():
                    widget.destroy()

            # 创建matplotlib图形
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False

            # 如果是实时更新且已有图形，更新数据
            if real_time and hasattr(self, 'training_canvas') and hasattr(self, 'training_fig'):
                fig = self.training_fig
                ax1, ax2 = self.training_axes

                # 清除旧数据
                ax1.clear()
                ax2.clear()
            else:
                # 创建新图形
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))
                self.training_fig = fig
                self.training_axes = (ax1, ax2)

            history = self.dl_training_history
            epochs = range(1, len(history['train_losses']) + 1)

            # 损失曲线
            ax1.plot(epochs, history['train_losses'], 'b-', label='训练损失', linewidth=1.5, marker='o', markersize=3)
            ax1.plot(epochs, history['val_losses'], 'r-', label='验证损失', linewidth=1.5, marker='s', markersize=3)
            ax1.set_title('模型损失变化', fontsize=11, fontweight='bold', fontfamily='Microsoft YaHei')  # 减小字体
            ax1.set_xlabel('训练轮次', fontsize=9, fontfamily='Microsoft YaHei')  # 减小字体
            ax1.set_ylabel('损失值', fontsize=9, fontfamily='Microsoft YaHei')  # 减小字体
            ax1.legend(fontsize=8)  # 减小图例字体
            ax1.tick_params(labelsize=8)  # 减小刻度标签字体
            ax1.grid(True, alpha=0.3)

            # 添加最新损失值标注
            if len(history['train_losses']) > 0:
                latest_train_loss = history['train_losses'][-1]
                latest_val_loss = history['val_losses'][-1]
                ax1.text(0.02, 0.98, f'最新训练损失: {latest_train_loss:.4f}\n最新验证损失: {latest_val_loss:.4f}',
                        transform=ax1.transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                        fontsize=8, fontfamily='Microsoft YaHei')  # 减小注释字体

            # 准确率曲线
            ax2.plot(epochs, history['train_accuracies'], 'b-', label='训练准确率', linewidth=1.5, marker='o', markersize=3)
            ax2.plot(epochs, history['val_accuracies'], 'r-', label='验证准确率', linewidth=1.5, marker='s', markersize=3)
            ax2.set_title('模型准确率变化', fontsize=11, fontweight='bold', fontfamily='Microsoft YaHei')  # 减小字体
            ax2.set_xlabel('训练轮次', fontsize=9, fontfamily='Microsoft YaHei')  # 减小字体
            ax2.set_ylabel('准确率 (%)', fontsize=9, fontfamily='Microsoft YaHei')  # 减小字体
            ax2.legend(fontsize=8)  # 减小图例字体
            ax2.tick_params(labelsize=8)  # 减小刻度标签字体
            ax2.grid(True, alpha=0.3)

            # 添加最新准确率标注
            if len(history['train_accuracies']) > 0:
                latest_train_acc = history['train_accuracies'][-1]
                latest_val_acc = history['val_accuracies'][-1]
                ax2.text(0.02, 0.98, f'最新训练准确率: {latest_train_acc:.2f}%\n最新验证准确率: {latest_val_acc:.2f}%',
                        transform=ax2.transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                        fontsize=8, fontfamily='Microsoft YaHei')  # 减小注释字体

            plt.tight_layout(rect=[0.08, 0, 1, 1])  # 左侧预留8%空间给y轴标题

            # 统一y轴标题位置
            ax1.yaxis.set_label_coords(-0.08, 0.5)
            ax2.yaxis.set_label_coords(-0.08, 0.5)

            # 嵌入到tkinter
            if not real_time or not hasattr(self, 'training_canvas'):
                canvas = FigureCanvasTkAgg(fig, self.dl_train_viz_frame)
                canvas.draw()
                canvas.get_tk_widget().pack(fill=BOTH, expand=True)
                self.training_canvas = canvas
            else:
                # 实时更新现有画布
                self.training_canvas.draw()

        except Exception as e:
            print(f"绘制训练曲线失败: {e}")

    def save_dl_model(self):
        """保存深度学习模型"""
        if not hasattr(self, 'dl_model') or self.dl_model is None:
            messagebox.showwarning("提示", "没有可保存的模型，请先完成训练")
            return

        try:
            import torch
            from datetime import datetime

            # 选择保存路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"fault_diagnosis_model_{timestamp}.pth"

            file_path = filedialog.asksaveasfilename(
                defaultextension=".pth",
                filetypes=[("PyTorch模型", "*.pth"), ("所有文件", "*.*")],
                initialfile=default_filename,
                title="保存深度学习模型"
            )

            if file_path:
                # 保存模型状态字典
                torch.save({
                    'model_state_dict': self.dl_model.state_dict(),
                    'model_config': {
                        'input_length': self.dl_model.input_length,
                        'num_classes': self.dl_model.num_classes,
                    },
                    'fault_types': self.dl_fault_types,
                    'training_history': self.dl_training_history if hasattr(self, 'dl_training_history') else None,
                    'scaler': self.dl_scaler
                }, file_path)

                messagebox.showinfo("成功", f"模型已保存至: {file_path}")

        except Exception as e:
            messagebox.showerror("保存失败", f"模型保存失败: {str(e)}")

    def load_dl_model(self):
        """加载深度学习模型"""
        try:
            import torch
            from dl_models import CNN1D

            # 选择模型文件
            file_path = filedialog.askopenfilename(
                filetypes=[("PyTorch模型", "*.pth"), ("所有文件", "*.*")],
                title="加载深度学习模型"
            )

            if not file_path:
                return

            # 加载模型
            checkpoint = torch.load(file_path, map_location='cpu', weights_only=False)

            # 获取模型配置
            model_config = checkpoint['model_config']
            fault_types = checkpoint['fault_types']

            # 创建模型
            model = CNN1D(
                input_length=model_config['input_length'],
                num_classes=model_config['num_classes']
            )

            # 加载模型权重
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()

            # 保存到实例变量
            self.dl_model = model
            self.dl_fault_types = fault_types
            if 'training_history' in checkpoint and checkpoint['training_history']:
                self.dl_training_history = checkpoint['training_history']
            if 'scaler' in checkpoint:
                self.dl_scaler = checkpoint['scaler']

            messagebox.showinfo("成功", f"模型加载成功！\n故障类型: {fault_types}")

            # 如果有训练历史，绘制曲线
            if hasattr(self, 'dl_training_history'):
                self._plot_training_curves()

        except Exception as e:
            messagebox.showerror("加载失败", f"模型加载失败: {str(e)}")

    def evaluate_dl_model(self):
        """评估深度学习模型"""
        if not hasattr(self, 'dl_model') or self.dl_model is None:
            messagebox.showwarning("提示", "没有可评估的模型，请先训练或加载模型")
            return

        if not hasattr(self, 'dl_test_data') or self.dl_test_data is None:
            messagebox.showwarning("提示", "没有测试数据，请先完成数据预处理")
            return

        try:
            from dl_models import VibrationDataset, ModelTrainer
            import torch
            from torch.utils.data import DataLoader

            # 获取测试数据
            X_test, y_test = self.dl_test_data
            fault_types = self.dl_fault_types

            # 创建测试数据集
            test_dataset = VibrationDataset(X_test, y_test)
            test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

            # 创建训练器进行评估
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            trainer = ModelTrainer(self.dl_model, device=device)

            # 评估模型
            self.dl_eval_info_label.config(text="正在评估模型...", fg=self.colors['primary'])

            results = trainer.evaluate(test_loader, fault_types)

            # 保存评估结果
            self.dl_eval_results = results

            # 显示评估结果
            accuracy = results['accuracy']
            info_text = f"模型评估完成！\n测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)"
            self.dl_eval_info_label.config(text=info_text, fg=self.colors['success'])

            # 显示详细结果
            self._show_evaluation_results(results)

        except Exception as e:
            error_msg = f"模型评估失败: {str(e)}"
            self.dl_eval_info_label.config(text=error_msg, fg=self.colors['error'])
            messagebox.showerror("评估失败", error_msg)

    def _show_evaluation_results(self, results):
        """显示评估结果"""
        try:
            # 清空显示区域
            for widget in self.dl_eval_results_frame.winfo_children():
                widget.destroy()

            # 创建结果显示文本
            import tkinter.scrolledtext as scrolledtext

            result_text = scrolledtext.ScrolledText(
                self.dl_eval_results_frame,
                height=10,
                font=("Consolas", 10),
                bg='white'
            )
            result_text.pack(fill=BOTH, expand=True, padx=10, pady=10)

            # 格式化分类报告
            report = results['classification_report']
            accuracy = results['accuracy']

            content = f"=== 模型评估报告 ===\n\n"
            content += f"总体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)\n\n"
            content += "各类别详细指标:\n"
            content += "-" * 50 + "\n"
            content += f"{'类别':<15} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'样本数':<10}\n"
            content += "-" * 50 + "\n"

            for class_name in self.dl_fault_types:
                if class_name in report:
                    metrics = report[class_name]
                    content += f"{class_name:<15} {metrics['precision']:<10.3f} {metrics['recall']:<10.3f} {metrics['f1-score']:<10.3f} {int(metrics['support']):<10}\n"

            content += "-" * 50 + "\n"

            # 添加宏平均和加权平均
            if 'macro avg' in report:
                macro = report['macro avg']
                content += f"{'宏平均':<15} {macro['precision']:<10.3f} {macro['recall']:<10.3f} {macro['f1-score']:<10.3f} {int(macro['support']):<10}\n"

            if 'weighted avg' in report:
                weighted = report['weighted avg']
                content += f"{'加权平均':<15} {weighted['precision']:<10.3f} {weighted['recall']:<10.3f} {weighted['f1-score']:<10.3f} {int(weighted['support']):<10}\n"

            result_text.insert('1.0', content)
            result_text.config(state='disabled')

        except Exception as e:
            print(f"显示评估结果失败: {e}")

    def show_confusion_matrix(self):
        """显示混淆矩阵"""
        if not hasattr(self, 'dl_eval_results') or self.dl_eval_results is None:
            messagebox.showwarning("提示", "请先进行模型评估")
            return

        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

            # 创建新窗口显示混淆矩阵
            cm_window = Toplevel(self.master)
            cm_window.title("混淆矩阵")
            cm_window.geometry("600x500")

            # 获取混淆矩阵数据和评估结果
            cm = self.dl_eval_results['confusion_matrix']
            fault_types = self.dl_fault_types
            targets = self.dl_eval_results['targets']
            predictions = self.dl_eval_results['predictions']

            # 获取实际存在的故障类型（在测试集中出现的类型）
            import numpy as np
            unique_targets = np.unique(targets)
            unique_predictions = np.unique(predictions)

            # 获取所有出现的类型（真实标签和预测标签的并集）
            all_unique_classes = np.unique(np.concatenate([unique_targets, unique_predictions]))

            # 过滤混淆矩阵，只保留实际存在的类型
            filtered_cm = cm[np.ix_(all_unique_classes, all_unique_classes)]
            filtered_fault_types = [fault_types[i] for i in all_unique_classes]

            print(f"原始故障类型: {fault_types}")
            print(f"测试集中的类型: {[fault_types[i] for i in unique_targets]}")
            print(f"过滤后的故障类型: {filtered_fault_types}")
            print(f"原始混淆矩阵形状: {cm.shape}")
            print(f"过滤后混淆矩阵形状: {filtered_cm.shape}")

            # 创建图形
            fig, ax = plt.subplots(figsize=(10, 8))

            # 设置中文字体
            import matplotlib.pyplot as plt
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False

            # 故障类型中文映射
            fault_type_chinese = {
                # 轴承故障类型
                'normal': '正常状态',
                'ball_fault': '滚动体故障',
                'inner_race_fault': '内圈故障',
                'outer_race_fault': '外圈故障',
                'IR': '内圈故障',
                'OR': '外圈故障',
                'B': '滚动体故障',
                'N': '正常状态',

                # 风机故障类型
                'rotor_unbalance': '转子不平衡',
                'rotor_misalignment': '转子不对中',
                'blade_fault': '叶片故障',
                'RU': '转子不平衡',
                'RM': '转子不对中',
                'BF': '叶片故障'
            }

            # 转换为中文标签（只转换实际存在的类型）
            chinese_labels = [fault_type_chinese.get(ft, ft) for ft in filtered_fault_types]

            # 绘制热力图（使用过滤后的混淆矩阵）
            sns.heatmap(filtered_cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=chinese_labels, yticklabels=chinese_labels, ax=ax,
                       annot_kws={'fontsize': 10})  # 减小注释字体
            ax.set_title('故障诊断混淆矩阵', fontsize=12, fontweight='bold', fontfamily='Microsoft YaHei')  # 减小标题字体
            ax.set_xlabel('预测故障类型', fontsize=10, fontfamily='Microsoft YaHei')  # 减小轴标签字体
            ax.set_ylabel('真实故障类型', fontsize=10, fontfamily='Microsoft YaHei')
            ax.tick_params(labelsize=9)  # 减小刻度标签字体

            plt.tight_layout(rect=[0.08, 0, 1, 1])  # 左侧预留8%空间给y轴标题

            # 统一y轴标题位置
            ax.yaxis.set_label_coords(-0.08, 0.5)

            # 嵌入到tkinter窗口
            canvas = FigureCanvasTkAgg(fig, cm_window)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=BOTH, expand=True)

        except Exception as e:
            messagebox.showerror("显示失败", f"显示混淆矩阵失败: {str(e)}")

    def show_tsne_visualization(self):
        """显示t-SNE可视化"""
        if not hasattr(self, 'dl_model') or self.dl_model is None:
            messagebox.showwarning("提示", "请先训练或加载模型")
            return

        if not hasattr(self, 'dl_test_data') or self.dl_test_data is None:
            messagebox.showwarning("提示", "没有测试数据，请先完成数据预处理")
            return

        try:
            import torch
            import numpy as np
            from sklearn.manifold import TSNE
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            from dl_models import VibrationDataset
            from torch.utils.data import DataLoader

            # 创建新窗口
            tsne_window = Toplevel(self.master)
            tsne_window.title("t-SNE特征可视化")
            tsne_window.geometry("800x600")

            # 添加状态标签
            status_label = Label(tsne_window, text="正在提取特征...", font=("微软雅黑", 10))
            status_label.pack(pady=10)

            # 获取测试数据
            X_test, y_test = self.dl_test_data
            fault_types = self.dl_fault_types

            # 创建数据集
            test_dataset = VibrationDataset(X_test, y_test)
            test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

            # 提取特征
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            self.dl_model.to(device)
            self.dl_model.eval()

            features = []
            labels = []

            with torch.no_grad():
                for data, target in test_loader:
                    data = data.to(device)

                    # 获取倒数第二层的特征
                    x = self.dl_model.pool1(torch.relu(self.dl_model.bn1(self.dl_model.conv1(data))))
                    x = self.dl_model.pool2(torch.relu(self.dl_model.bn2(self.dl_model.conv2(x))))
                    x = self.dl_model.pool3(torch.relu(self.dl_model.bn3(self.dl_model.conv3(x))))
                    x = x.view(x.size(0), -1)
                    x = torch.relu(self.dl_model.fc1(x))
                    x = self.dl_model.dropout1(x)
                    x = torch.relu(self.dl_model.fc2(x))  # 使用fc2层的输出作为特征

                    features.append(x.cpu().numpy())
                    labels.append(target.numpy())

            # 合并特征和标签
            features = np.vstack(features)
            labels = np.hstack(labels)

            status_label.config(text="正在进行t-SNE降维...")
            tsne_window.update()

            # 如果样本太多，进行采样
            if len(features) > 1000:
                indices = np.random.choice(len(features), 1000, replace=False)
                features = features[indices]
                labels = labels[indices]

            # 执行t-SNE
            tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(features)-1))
            features_2d = tsne.fit_transform(features)

            status_label.config(text="正在绘制可视化图...")
            tsne_window.update()

            # 设置中文字体
            import matplotlib.pyplot as plt
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False

            # 故障类型中文映射
            fault_type_chinese = {
                # 轴承故障类型
                'normal': '正常状态',
                'ball_fault': '滚动体故障',
                'inner_race_fault': '内圈故障',
                'outer_race_fault': '外圈故障',
                'IR': '内圈故障',
                'OR': '外圈故障',
                'B': '滚动体故障',
                'N': '正常状态',

                # 风机故障类型
                'rotor_unbalance': '转子不平衡',
                'rotor_misalignment': '转子不对中',
                'blade_fault': '叶片故障',
                'RU': '转子不平衡',
                'RM': '转子不对中',
                'BF': '叶片故障'
            }

            # 创建图形
            fig, ax = plt.subplots(figsize=(12, 9))

            # 为每个类别分配颜色
            colors = plt.cm.Set3(np.linspace(0, 1, len(fault_types)))

            for i, fault_type in enumerate(fault_types):
                mask = labels == i
                chinese_name = fault_type_chinese.get(fault_type, fault_type)
                ax.scatter(features_2d[mask, 0], features_2d[mask, 1],
                          c=[colors[i]], label=chinese_name, alpha=0.7, s=60)

            ax.set_title('深度学习特征t-SNE降维可视化', fontsize=16, fontweight='bold', fontfamily='Microsoft YaHei')
            ax.set_xlabel('t-SNE第一主成分', fontsize=13, fontfamily='Microsoft YaHei')
            ax.set_ylabel('t-SNE第二主成分', fontsize=13, fontfamily='Microsoft YaHei')
            ax.legend(fontsize=12)
            ax.grid(True, alpha=0.3)

            plt.tight_layout(rect=[0.08, 0, 1, 1])  # 左侧预留8%空间给y轴标题

            # 统一y轴标题位置
            ax.yaxis.set_label_coords(-0.08, 0.5)

            # 移除状态标签
            status_label.destroy()

            # 嵌入到tkinter窗口
            canvas = FigureCanvasTkAgg(fig, tsne_window)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=BOTH, expand=True)

        except Exception as e:
            messagebox.showerror("可视化失败", f"t-SNE可视化失败: {str(e)}")

    def select_prediction_file(self):
        """选择预测文件"""
        try:
            file_path = filedialog.askopenfilename(
                filetypes=[
                    ("MAT文件", "*.mat"),
                    ("Excel文件", "*.xlsx *.xls"),
                    ("CSV文件", "*.csv"),
                    ("所有文件", "*.*")
                ],
                title="选择待预测的振动数据文件"
            )

            if file_path:
                self.prediction_file_path = file_path
                filename = os.path.basename(file_path)
                self.dl_predict_info_label.config(
                    text=f"已选择文件: {filename}",
                    fg=self.colors['primary']
                )

        except Exception as e:
            messagebox.showerror("文件选择失败", f"选择文件失败: {str(e)}")

    def select_attachment_for_prediction(self):
        """选择文件管理中的附件进行预测"""
        try:
            if not self.attachments:
                messagebox.showinfo("提示", "文件管理中没有附件，请先添加附件")
                return

            # 创建附件选择窗口
            attachment_window = Toplevel(self.master)
            attachment_window.title("选择附件进行预测")
            attachment_window.geometry("600x400")
            attachment_window.transient(self.master)
            attachment_window.configure(bg='white')

            # 标题
            Label(attachment_window, text="选择要用于预测的附件",
                  font=("微软雅黑", 14, "bold"),
                  bg='white', fg=self.colors['primary']).pack(pady=20)

            # 附件列表框架
            list_frame = Frame(attachment_window, bg='white')
            list_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

            # 创建列表框
            listbox_frame = Frame(list_frame, bg='white')
            listbox_frame.pack(fill=BOTH, expand=True)

            # 滚动条
            scrollbar = Scrollbar(listbox_frame)
            scrollbar.pack(side=RIGHT, fill=Y)

            # 列表框
            attachment_listbox = Listbox(listbox_frame,
                                       yscrollcommand=scrollbar.set,
                                       font=("微软雅黑", 10),
                                       height=15)
            attachment_listbox.pack(side=LEFT, fill=BOTH, expand=True)
            scrollbar.config(command=attachment_listbox.yview)

            # 填充附件列表
            for i, att in enumerate(self.attachments):
                # 显示附件信息
                file_info = f"{i+1}. {att['name']}"
                if att['is_image']:
                    file_info += " (图片文件)"
                else:
                    # 根据文件扩展名确定文件类型
                    file_ext = os.path.splitext(att['name'])[1].lower()
                    if file_ext in ['.xlsx', '.xls']:
                        file_type = "Excel"
                    elif file_ext == '.mat':
                        file_type = "MAT"
                    elif file_ext == '.pdf':
                        file_type = "PDF"
                    elif file_ext in ['.txt', '.docx']:
                        file_type = "文档"
                    elif file_ext == '.csv':
                        file_type = "CSV"
                    else:
                        file_type = "数据"
                    file_info += f" ({file_type}文件)"
                attachment_listbox.insert(END, file_info)

            # 按钮框架
            button_frame = Frame(attachment_window, bg='white')
            button_frame.pack(fill=X, padx=20, pady=20)

            def select_attachment():
                """选择附件"""
                selection = attachment_listbox.curselection()
                if not selection:
                    messagebox.showwarning("提示", "请选择一个附件")
                    return

                selected_index = selection[0]
                selected_attachment = self.attachments[selected_index]

                # 检查附件类型
                if selected_attachment['is_image']:
                    messagebox.showwarning("提示", "图片文件不能用于故障预测，请选择数据文件")
                    return

                # 检查是否有数据内容
                if isinstance(selected_attachment['content'], dict):
                    # 如果是分析后的数据，提取原始信号
                    if 'visual_data' in selected_attachment['content']:
                        visual_data = selected_attachment['content']['visual_data']
                        if 'time_signal' in visual_data:
                            # 使用时域信号数据
                            self.prediction_data_source = "attachment"
                            self.prediction_attachment_data = visual_data['time_signal']
                            self.prediction_file_path = f"附件: {selected_attachment['name']}"

                            self.dl_predict_info_label.config(
                                text=f"已选择附件: {selected_attachment['name']} (时域信号数据)",
                                fg=self.colors['primary']
                            )
                            attachment_window.destroy()
                            return

                    messagebox.showwarning("提示", "所选附件不包含可用的振动信号数据")
                    return
                else:
                    # 直接使用附件内容
                    try:
                        # 尝试解析数据
                        import numpy as np

                        # 如果是文本数据，尝试转换为数值
                        content = selected_attachment['content']
                        if isinstance(content, str):
                            # 尝试按行分割并转换为数值
                            lines = content.strip().split('\n')
                            data = []
                            for line in lines:
                                try:
                                    # 尝试多种分隔符
                                    if ',' in line:
                                        values = [float(x.strip()) for x in line.split(',') if x.strip()]
                                    elif '\t' in line:
                                        values = [float(x.strip()) for x in line.split('\t') if x.strip()]
                                    else:
                                        values = [float(x.strip()) for x in line.split() if x.strip()]
                                    data.extend(values)
                                except ValueError:
                                    continue

                            if data:
                                self.prediction_data_source = "attachment"
                                self.prediction_attachment_data = np.array(data)
                                self.prediction_file_path = f"附件: {selected_attachment['name']}"

                                self.dl_predict_info_label.config(
                                    text=f"已选择附件: {selected_attachment['name']} ({len(data)}个数据点)",
                                    fg=self.colors['primary']
                                )
                                attachment_window.destroy()
                                return

                        messagebox.showwarning("提示", "无法从所选附件中提取数值数据")

                    except Exception as e:
                        messagebox.showerror("错误", f"解析附件数据失败: {str(e)}")

            def cancel_selection():
                """取消选择"""
                attachment_window.destroy()

            # 按钮
            Button(button_frame, text="✅ 选择", font=("微软雅黑", 11, "bold"),
                   bg=self.colors['success'], fg='white', relief='flat',
                   padx=20, pady=8, command=select_attachment).pack(side=LEFT, padx=(0, 10))

            Button(button_frame, text="❌ 取消", font=("微软雅黑", 11),
                   bg=self.colors['surface'], fg=self.colors['text_primary'], relief='flat',
                   padx=20, pady=8, command=cancel_selection).pack(side=LEFT)

        except Exception as e:
            messagebox.showerror("选择附件失败", f"选择附件失败: {str(e)}")

    def clear_prediction_file(self):
        """删除已选择的预测文件"""
        try:
            # 清除文件路径
            if hasattr(self, 'prediction_file_path'):
                delattr(self, 'prediction_file_path')

            # 清除附件数据
            if hasattr(self, 'prediction_attachment_data'):
                delattr(self, 'prediction_attachment_data')

            # 清除数据源标识
            if hasattr(self, 'prediction_data_source'):
                delattr(self, 'prediction_data_source')

            # 清除预处理数据
            if hasattr(self, 'preprocessed_prediction_data'):
                delattr(self, 'preprocessed_prediction_data')

            # 更新状态标签
            self.dl_predict_info_label.config(
                text="请选择预测文件或附件",
                fg=self.colors['text_secondary']
            )

            messagebox.showinfo("提示", "已清除选择的文件")

        except Exception as e:
            messagebox.showerror("清除失败", f"清除文件选择失败: {str(e)}")

    def preprocess_prediction_data(self):
        """对预测数据进行Z-score标准化预处理"""
        if not hasattr(self, 'prediction_file_path') or not self.prediction_file_path:
            messagebox.showwarning("提示", "请先选择预测文件或附件")
            return

        if not hasattr(self, 'dl_scaler') or self.dl_scaler is None:
            messagebox.showwarning("提示", "请先完成模型训练或加载已训练的模型（需要标准化器）")
            return

        try:
            import numpy as np
            from sklearn.preprocessing import StandardScaler

            # 更新状态
            self.dl_predict_info_label.config(text="正在进行数据预处理...", fg=self.colors['primary'])

            # 检查数据源类型
            if hasattr(self, 'prediction_data_source') and self.prediction_data_source == "attachment":
                # 使用附件数据
                if hasattr(self, 'prediction_attachment_data'):
                    data = self.prediction_attachment_data
                    print(f"使用附件数据，数据长度: {len(data)}")
                else:
                    raise ValueError("附件数据不可用")
            else:
                # 加载文件数据
                file_path = self.prediction_file_path
                file_ext = os.path.splitext(file_path)[1].lower()

                if file_ext == '.mat':
                    data = self._load_mat_file(file_path)
                    if data is None:
                        raise ValueError("无法从MAT文件中提取有效数据")
                elif file_ext in ['.xlsx', '.xls']:
                    import pandas as pd
                    df = pd.read_excel(file_path)
                    data = df.iloc[:, 0].values
                elif file_ext == '.csv':
                    import pandas as pd
                    df = pd.read_csv(file_path)
                    data = df.iloc[:, 0].values
                else:
                    raise ValueError(f"不支持的文件格式: {file_ext}")

            # 数据预处理
            original_length = len(data)
            print(f"原始数据长度: {original_length}")

            # 分割数据为固定长度的样本
            sample_length = 1024  # 与训练时保持一致
            if len(data) < sample_length:
                # 如果数据长度不足，进行零填充
                padded_data = np.zeros(sample_length)
                padded_data[:len(data)] = data
                data = padded_data
                print(f"数据长度不足，已零填充至: {sample_length}")

            # 分割为多个样本
            num_samples = len(data) // sample_length
            if num_samples == 0:
                num_samples = 1
                samples = data[:sample_length].reshape(1, -1)
            else:
                samples = data[:num_samples * sample_length].reshape(num_samples, sample_length)

            print(f"分割后样本数: {num_samples}, 样本形状: {samples.shape}")

            # Z-score标准化 (使用训练时的标准化器)
            samples_scaled = self.dl_scaler.transform(samples)

            # 验证Z-score标准化效果
            mean_after = np.mean(samples_scaled)
            std_after = np.std(samples_scaled)
            print(f"Z-score标准化验证: 均值={mean_after:.6f}, 标准差={std_after:.6f}")

            # 保存预处理后的数据
            self.preprocessed_prediction_data = samples_scaled

            # 更新状态
            self.dl_predict_info_label.config(
                text=f"数据预处理完成！样本数: {num_samples}, Z-score标准化已应用",
                fg=self.colors['success']
            )

            messagebox.showinfo("预处理完成",
                               f"数据预处理完成！\n"
                               f"原始数据长度: {original_length}\n"
                               f"处理后样本数: {num_samples}\n"
                               f"每个样本长度: {sample_length}\n"
                               f"Z-score标准化: 均值≈{mean_after:.3f}, 标准差≈{std_after:.3f}")

        except Exception as e:
            error_msg = f"数据预处理失败: {str(e)}"
            self.dl_predict_info_label.config(text=error_msg, fg=self.colors['error'])
            messagebox.showerror("预处理失败", error_msg)

    def start_dl_prediction(self):
        """开始深度学习预测"""
        if not hasattr(self, 'dl_model') or self.dl_model is None:
            messagebox.showwarning("提示", "请先训练或加载模型")
            return

        if not hasattr(self, 'prediction_file_path') or not self.prediction_file_path:
            messagebox.showwarning("提示", "请先选择预测文件或附件")
            return

        try:
            import torch
            import numpy as np
            from dl_models import VibrationDataset
            from torch.utils.data import DataLoader
            import scipy.io

            # 检查是否有预处理后的数据
            if hasattr(self, 'preprocessed_prediction_data'):
                # 使用预处理后的数据
                self.dl_predict_info_label.config(text="使用预处理后的数据进行预测...", fg=self.colors['primary'])
                data = self.preprocessed_prediction_data
                print(f"使用预处理后的数据，样本形状: {data.shape}")
            else:
                # 使用原始数据并进行基础预处理
                self.dl_predict_info_label.config(text="正在加载预测数据...", fg=self.colors['primary'])

                # 检查数据源类型
                if hasattr(self, 'prediction_data_source') and self.prediction_data_source == "attachment":
                    # 使用附件数据
                    if hasattr(self, 'prediction_attachment_data'):
                        data = self.prediction_attachment_data
                        print(f"使用附件数据，数据长度: {len(data)}")
                    else:
                        raise ValueError("附件数据不可用")
                else:
                    # 加载文件数据
                    file_path = self.prediction_file_path
                    file_ext = os.path.splitext(file_path)[1].lower()

                    if file_ext == '.mat':
                        # 加载MAT文件
                        data = self._load_mat_file(file_path)
                        if data is None:
                            raise ValueError("无法从MAT文件中提取有效数据")
                    elif file_ext in ['.xlsx', '.xls']:
                        # 加载Excel文件
                        import pandas as pd
                        df = pd.read_excel(file_path)
                        data = df.iloc[:, 0].values  # 取第一列
                    elif file_ext == '.csv':
                        # 加载CSV文件
                        import pandas as pd
                        df = pd.read_csv(file_path)
                        data = df.iloc[:, 0].values  # 取第一列
                    else:
                        raise ValueError(f"不支持的文件格式: {file_ext}")

                # 数据预处理
                self.dl_predict_info_label.config(text="正在预处理数据...", fg=self.colors['primary'])

                # 获取样本长度（与训练时一致）
                sample_length = self.dl_model.input_length

                # 如果数据长度不够，进行填充或截断
                if len(data) < sample_length:
                    # 填充
                    data = np.pad(data, (0, sample_length - len(data)), mode='constant')
                elif len(data) > sample_length:
                    # 截断或分段
                    num_segments = len(data) // sample_length
                    data = data[:num_segments * sample_length].reshape(num_segments, sample_length)
                else:
                    data = data.reshape(1, sample_length)

                # Z-score标准化（如果有保存的scaler）
                if hasattr(self, 'dl_scaler') and self.dl_scaler is not None:
                    original_shape = data.shape
                    data_reshaped = data.reshape(-1, sample_length)
                    data_scaled = self.dl_scaler.transform(data_reshaped)
                    data = data_scaled.reshape(original_shape)
                    print("应用Z-score标准化")
                else:
                    messagebox.showwarning("提示", "缺少标准化器，建议先进行数据预处理")

            # 添加通道维度
            if data.ndim == 2:
                data = data.reshape(data.shape[0], data.shape[1], 1)

            # 创建数据集
            dummy_labels = np.zeros(data.shape[0])  # 创建虚拟标签
            predict_dataset = VibrationDataset(data, dummy_labels)
            predict_loader = DataLoader(predict_dataset, batch_size=32, shuffle=False)

            # 进行预测
            self.dl_predict_info_label.config(text="正在进行故障预测...", fg=self.colors['primary'])

            device = 'cuda' if torch.cuda.is_available() else 'cpu'
            self.dl_model.to(device)
            self.dl_model.eval()

            all_predictions = []
            all_probabilities = []

            with torch.no_grad():
                for data_batch, _ in predict_loader:
                    data_batch = data_batch.to(device)
                    output = self.dl_model(data_batch)
                    probs = torch.softmax(output, dim=1)
                    pred = output.argmax(dim=1)

                    all_predictions.extend(pred.cpu().numpy())
                    all_probabilities.extend(probs.cpu().numpy())

            # 处理预测结果
            predictions = np.array(all_predictions)
            probabilities = np.array(all_probabilities)

            # 显示预测结果
            self._show_prediction_results(predictions, probabilities)

        except Exception as e:
            error_msg = f"预测失败: {str(e)}"
            self.dl_predict_info_label.config(text=error_msg, fg=self.colors['error'])
            messagebox.showerror("预测失败", error_msg)

    def _show_prediction_results(self, predictions, probabilities):
        """显示预测结果"""
        try:
            import numpy as np

            # 清空结果显示区域
            for widget in self.dl_predict_results_frame.winfo_children():
                widget.destroy()

            # 统计预测结果
            fault_types = self.dl_fault_types
            unique_preds, counts = np.unique(predictions, return_counts=True)

            # 计算平均置信度
            avg_confidence = np.mean(np.max(probabilities, axis=1))

            # 最终预测（多数投票）
            if len(predictions) > 1:
                final_prediction = np.bincount(predictions).argmax()
            else:
                final_prediction = predictions[0]

            final_fault_type = fault_types[final_prediction]
            final_confidence = np.mean(probabilities[:, final_prediction])

            # 存储深度学习预测结果，供DeepSeek分析使用
            self.dl_prediction_results = {
                'final_prediction': final_prediction,
                'final_fault_type': final_fault_type,
                'final_confidence': final_confidence,
                'avg_confidence': avg_confidence,
                'predictions': predictions,
                'probabilities': probabilities,
                'fault_types': fault_types,
                'prediction_counts': dict(zip(unique_preds, counts)),
                'prediction_file': getattr(self, 'prediction_file_path', 'Unknown')
            }

            # 根据深度学习预测结果控制DIO输出
            fault_detected = final_fault_type.lower() != 'normal'

            # 添加详细的调试信息
            print(f"🔍 DIO控制调试信息:")
            print(f"   预测结果: {final_fault_type}")
            print(f"   故障检测: {fault_detected}")
            print(f"   DIO功能启用: {self.fault_detection_enabled}")
            print(f"   DAQ硬件可用: {DAQ_AVAILABLE}")
            print(f"   当前DIO状态: {self.dio_params['current_state']}")

            self.update_dio_based_on_fault_detection(fault_detected)

            # 自动提示用户可以向DeepSeek咨询分析结果
            self.auto_suggest_deepseek_analysis()

            # 创建结果显示
            result_frame = Frame(self.dl_predict_results_frame, bg='white')
            result_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

            # 主要预测结果
            main_result_frame = LabelFrame(result_frame, text="预测结果",
                                         font=("微软雅黑", 12, "bold"), bg='white')
            main_result_frame.pack(fill=X, pady=(0, 10))

            # 根据故障类型设置颜色
            if final_fault_type == 'normal':
                result_color = self.colors['success']
                status_text = "设备状态正常"
            else:
                result_color = self.colors['error']
                status_text = f"检测到故障: {final_fault_type}"

            Label(main_result_frame, text=status_text,
                  font=("微软雅黑", 14, "bold"), fg=result_color, bg='white').pack(pady=10)

            Label(main_result_frame, text=f"置信度: {final_confidence:.2%}",
                  font=("微软雅黑", 12), bg='white').pack()

            # 详细统计
            detail_frame = LabelFrame(result_frame, text="详细统计",
                                    font=("微软雅黑", 12, "bold"), bg='white')
            detail_frame.pack(fill=BOTH, expand=True)

            # 创建统计表格
            import tkinter.ttk as ttk

            # 表格标题
            columns = ('故障类型', '预测次数', '占比', '平均置信度')
            tree = ttk.Treeview(detail_frame, columns=columns, show='headings', height=6)

            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120, anchor='center')

            # 填充数据
            for i, fault_type in enumerate(fault_types):
                count = counts[unique_preds == i][0] if i in unique_preds else 0
                ratio = count / len(predictions) if len(predictions) > 0 else 0
                avg_conf = np.mean(probabilities[:, i])

                tree.insert('', 'end', values=(
                    fault_type,
                    count,
                    f"{ratio:.1%}",
                    f"{avg_conf:.2%}"
                ))

            tree.pack(fill=BOTH, expand=True, padx=10, pady=10)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(detail_frame, orient="vertical", command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side="right", fill="y")

            # 更新状态标签
            if hasattr(self, 'prediction_data_source') and self.prediction_data_source == "attachment":
                filename = self.prediction_file_path  # 已经包含"附件: "前缀
            else:
                filename = os.path.basename(self.prediction_file_path)
            status_text = f"预测完成！{filename}\n最终结果: {final_fault_type} (置信度: {final_confidence:.2%})"
            self.dl_predict_info_label.config(text=status_text, fg=self.colors['success'])

        except Exception as e:
            print(f"显示预测结果失败: {e}")

    def _load_mat_file(self, file_path):
        """加载MAT文件并提取振动数据"""
        try:
            import scipy.io
            import numpy as np

            mat_data = scipy.io.loadmat(file_path)
            valid_keys = [k for k in mat_data.keys() if not k.startswith('__')]

            # 寻找合适的数据变量
            target_key = None
            for k in valid_keys:
                if '_time' in k.lower():
                    target_key = k
                    break

            if not target_key:
                for k in valid_keys:
                    if isinstance(mat_data[k], np.ndarray) and mat_data[k].size > 100:
                        target_key = k
                        break

            if target_key:
                signal = mat_data[target_key].ravel()
                return signal
            else:
                return None

        except Exception as e:
            print(f"加载MAT文件失败: {e}")
            return None

    # ============== 发送问题 / 多轮对话 ==============
    def send_question(self, event=None):
        """发送问题到AI"""
        question = self.input_text.get("1.0", END).strip()
        if not question and not self.attachments:
            messagebox.showinfo("提示", "请输入问题或添加附件")
            return
        if self.is_processing:
            messagebox.showinfo("处理中", "正在处理上一个请求，请稍候")
            return
        self.send_button.config(state='disabled')
        self.stop_button.config(state='normal')  # 启用停止按钮
        self.stop_generation = False  # 重置停止标志
        self.is_processing = True
        full_content = question
        if self.attachments:
            full_content += "\n\n附件内容："
            for att in self.attachments:
                if isinstance(att['content'], dict):
                    c_text = att['content'].get('analysis_text', '')
                else:
                    c_text = att['ocr_text'] if att['is_image'] else att['content']
                if len(c_text) > 1000:
                    c_text = f"{c_text[:1000]}..."
                full_content += f"\n\n【{att['name']}】\n{c_text}"

        # 更新UI显示和记录用户问题 - 在问题前添加额外的换行
        self.append_history("\n")
        self.update_history("用户", question)
        self.log_message("用户", question)

        # 清空输入框
        self.input_text.delete("1.0", END)

        # 更新对话上下文
        self.messages.append({"role": "user", "content": full_content})

        # 启动新线程处理问题
        threading.Thread(target=self.process_question, daemon=True).start()

    def prepare_comprehensive_analysis_data(self):
        """准备综合分析数据，包括时频域数据、深度学习预测结果、特征指标等，用于发送给DeepSeek"""
        # 确保numpy可用
        import numpy as np

        # 检查是否有包含时频数据的附件
        time_freq_attachments = [att for att in self.attachments
                               if isinstance(att['content'], dict)
                               and "visual_data" in att['content']]

        if not time_freq_attachments:
            return ""  # 没有时频数据

        result = "\n\n【综合诊断分析数据】\n"

        # 1. 深度学习预测结果
        if hasattr(self, 'dl_prediction_results') and self.dl_prediction_results:
            dl_results = self.dl_prediction_results
            result += "\n=== 深度学习模型预测结果 ===\n"
            result += f"诊断结论: {dl_results['final_fault_type']}\n"
            result += f"预测置信度: {dl_results['final_confidence']:.2%}\n"
            result += f"平均置信度: {dl_results['avg_confidence']:.2%}\n"
            result += f"预测文件: {dl_results['prediction_file']}\n"

            # 详细预测分布
            result += "\n预测结果分布:\n"
            for fault_idx, count in dl_results['prediction_counts'].items():
                fault_name = dl_results['fault_types'][fault_idx]
                percentage = count / len(dl_results['predictions']) * 100
                result += f"  {fault_name}: {count}次 ({percentage:.1f}%)\n"

            # 各类别置信度
            result += "\n各类别平均置信度:\n"
            avg_probs = np.mean(dl_results['probabilities'], axis=0)
            for i, fault_type in enumerate(dl_results['fault_types']):
                result += f"  {fault_type}: {avg_probs[i]:.2%}\n"
            result += "\n"

        for att in time_freq_attachments:
            data = att['content']['visual_data']
            signal = data["time_signal"]
            fs = data["fs"]
            freq_axis = data["freq_axis"]
            env_spectrum = data["envelope_spectrum"]

            # 2. 基本信息
            result += f"=== 数据基本信息 ===\n"
            result += f"文件名: {att['name']}\n"
            result += f"采样频率: {fs} Hz\n"
            result += f"数据长度: {len(signal):,} 点\n"
            result += f"时间长度: {len(signal)/fs:.2f} 秒\n\n"

            # 3. 计算完整的特征指标
            time_metrics = self.calculate_time_domain_metrics(signal)
            freq_metrics = self.calculate_frequency_metrics(signal, fs)
            fault_metrics = self.calculate_fault_indicators(signal, fs)

            # 时域特征指标
            result += "=== 时域特征指标 ===\n"
            for name, value in time_metrics.items():
                result += f"{name}: {value}\n"
            result += "\n"

            # 频域特征指标
            result += "=== 频域特征指标 ===\n"
            for name, value in freq_metrics.items():
                result += f"{name}: {value}\n"
            result += "\n"

            # 故障特征频率指标
            result += "=== 故障特征频率指标 ===\n"
            for name, value in fault_metrics.items():
                result += f"{name}: {value}\n"
            result += "\n"

            # 4. 故障特征频率计算（如果有轴承参数）
            bearing_params = self.parse_filename_params(att['name'])
            if bearing_params:
                result += "=== 理论故障特征频率 ===\n"
                fault_freqs = bearing_params['fault_freqs']
                for fault_type, freq_val in fault_freqs.items():
                    result += f"{fault_type}: {freq_val:.2f} Hz\n"
                result += "\n"

                # 存储故障频率供后续使用
                self.bearing_fault_freqs = fault_freqs
            elif hasattr(self, 'bearing_fault_freqs') and self.bearing_fault_freqs:
                result += "=== 理论故障特征频率 ===\n"
                for fault_type, freq_val in self.bearing_fault_freqs.items():
                    result += f"{fault_type}: {freq_val:.2f} Hz\n"
                result += "\n"

            # 5. 频谱峰值分析
            peak_indices = np.argsort(-env_spectrum)[:5]
            peak_freqs = [freq_axis[i] for i in peak_indices]
            peak_amps = [env_spectrum[i] for i in peak_indices]

            result += "=== 频谱峰值分析 ===\n"
            result += "前5个最显著峰值:\n"
            for i in range(min(5, len(peak_freqs))):
                result += f"  峰值{i+1}: {peak_freqs[i]:.2f} Hz (幅值: {peak_amps[i]:.4f})\n"
            result += "\n"

            # 6. 故障频率匹配分析
            if hasattr(self, 'bearing_fault_freqs') and self.bearing_fault_freqs:
                result += "=== 故障频率匹配分析 ===\n"
                for fault_name, fault_freq in self.bearing_fault_freqs.items():
                    matches = []
                    for i, peak_freq in enumerate(peak_freqs):
                        error = abs(peak_freq - fault_freq) / fault_freq
                        if error < 0.1:  # 10%误差范围内
                            matches.append(f"峰值{i+1} ({peak_freq:.2f}Hz) 误差: {error:.1%}")
                        # 检查谐波
                        for harmonic in range(2, 4):
                            harmonic_freq = fault_freq * harmonic
                            if harmonic_freq < max(freq_axis):  # 确保谐波频率在分析范围内
                                error_harmonic = abs(peak_freq - harmonic_freq) / harmonic_freq
                                if error_harmonic < 0.1:
                                    matches.append(f"峰值{i+1} ({peak_freq:.2f}Hz) 接近{harmonic}次谐波 ({harmonic_freq:.2f}Hz) 误差: {error_harmonic:.1%}")

                    if matches:
                        result += f"{fault_name} ({fault_freq:.2f}Hz) 匹配:\n"
                        for match in matches:
                            result += f"  - {match}\n"
                    else:
                        result += f"{fault_name} ({fault_freq:.2f}Hz): 无明显匹配\n"
                result += "\n"

            # 7. 小波变换特征（如果有）
            if "wavelet_transform" in data:
                coef = data["wavelet_transform"]["coef"]
                scales = data["wavelet_transform"]["scales"]

                # 计算每个尺度的能量分布
                scale_energies = np.sum(np.abs(coef)**2, axis=1)
                max_energy_scale = scales[np.argmax(scale_energies)]

                # 找出能量集中的时间段
                time_energy = np.sum(np.abs(coef)**2, axis=0)
                energy_threshold = np.mean(time_energy) + np.std(time_energy)
                high_energy_times = np.where(time_energy > energy_threshold)[0]
                if len(high_energy_times) > 0:
                    high_energy_start = high_energy_times[0] / fs
                    high_energy_end = high_energy_times[-1] / fs
                    high_energy_duration = high_energy_end - high_energy_start
                else:
                    high_energy_start = high_energy_end = high_energy_duration = 0

                result += "=== 小波变换特征 ===\n"
                result += f"最大能量尺度: {max_energy_scale:.2f}\n"
                if high_energy_duration > 0:
                    result += f"高能量时间段: {high_energy_start:.4f}s - {high_energy_end:.4f}s (持续{high_energy_duration:.4f}s)\n"

                # 小波系数统计特征
                coef_mean = np.mean(np.abs(coef))
                coef_std = np.std(np.abs(coef))
                coef_max = np.max(np.abs(coef))
                result += f"小波系数统计: 均值={coef_mean:.4f}, 标准差={coef_std:.4f}, 最大值={coef_max:.4f}\n"

                # 各尺度能量分布
                result += "各尺度能量分布:\n"
                top_scales_idx = np.argsort(-scale_energies)[:3]
                for idx in top_scales_idx:
                    scale = scales[idx]
                    energy = scale_energies[idx]
                    freq_approx = fs/(2*scale)
                    result += f"  尺度{scale:.1f}: 能量={energy:.2e}, 频率≈{freq_approx:.2f}Hz\n"
                result += "\n"

        return result

    def auto_suggest_deepseek_analysis(self):
        """深度学习预测完成后自动建议用户向DeepSeek咨询分析"""
        try:
            if hasattr(self, 'dl_prediction_results') and self.dl_prediction_results:
                dl_results = self.dl_prediction_results

                # 构建建议的问题
                suggested_question = f"""请分析深度学习模型的诊断结果：

诊断结论：{dl_results['final_fault_type']}
预测置信度：{dl_results['final_confidence']:.2%}

请基于诊断结果和特征指标数据，提供详细的分析报告，包括：
1. 诊断结果的可靠性评估
2. 特征指标与诊断结果的一致性分析
3. 维护建议和风险评估
4. 后续监测建议"""

                # 在输入框中填入建议的问题
                self.input_text.delete("1.0", END)
                self.input_text.insert("1.0", suggested_question)

                # 显示提示信息
                messagebox.showinfo("分析建议",
                    f"✅ 深度学习预测完成！\n\n"
                    f"诊断结果：{dl_results['final_fault_type']}\n"
                    f"置信度：{dl_results['final_confidence']:.2%}\n\n"
                    f"💡 已在输入框中为您准备了分析问题，\n"
                    f"点击发送即可获得DeepSeek的详细分析报告。")

        except Exception as e:
            print(f"自动建议分析失败: {str(e)}")

    def get_bearing_knowledge_prompt(self):
        """获取轴承知识库提示词"""
        # 尝试从外部模块获取轴承知识库
        external_knowledge = get_bearing_knowledge_base()
        if external_knowledge:
            return external_knowledge

        # 如果外部模块不可用，返回基础提示词
        return """你是DeepSeek故障诊断助手中的专业诊断引擎，请以标准设备诊断报告的格式提供专业、精确、详尽的回答。"""

    def get_wind_turbine_knowledge_prompt(self):
        """获取风机知识库提示词"""
        # 尝试从外部模块获取风机知识库
        external_knowledge = get_wind_turbine_knowledge_base()
        if external_knowledge:
            return external_knowledge

        # 如果外部模块不可用，返回基础提示词
        return """你是DeepSeek风机故障诊断助手，专门负责风机设备的故障诊断和分析。
你的主要职责是：
1. 分析风机振动信号的特征指标
2. 根据深度学习模型的预测结果生成专业报告
3. 提供基于预测结果的维护建议
4. 解读风机特有的故障模式和特征频率

请以专业的风机故障诊断报告格式提供详尽的分析。"""

    def process_question(self):
        """处理问题，调用API获取回答（流式输出）"""
        try:
            # 准备综合分析数据
            comprehensive_data = self.prepare_comprehensive_analysis_data()
            if comprehensive_data:
                # 将综合分析数据添加到最后一条用户消息中
                self.messages[-1]["content"] += comprehensive_data

                # 检查是否需要使用轴承知识库
                last_message = self.messages[-1]["content"].lower()
                bearing_keywords = ["轴承", "bearing", "内圈", "外圈", "滚动体", "保持架", "bpfo", "bpfi", "bsf", "ftf"]

                if any(keyword in last_message for keyword in bearing_keywords):
                    bearing_knowledge_prompt = self.get_bearing_knowledge_prompt()

                    # 检查是否已有系统提示词
                    if self.messages[0]["role"] == "system":
                        # 更新系统提示词
                        self.messages[0]["content"] = bearing_knowledge_prompt
                    else:
                        # 如果没有系统提示词，添加一个
                        self.messages.insert(0, {"role": "system", "content": bearing_knowledge_prompt})

            self.set_status("正在连接API...")
            client = OpenAI(api_key=self.key, base_url=self.api_url)
            self.set_status("生成回答中...")
            response = client.chat.completions.create(
                    model="deepseek-reasoner",
                    messages=self.messages,
                    temperature=self.api_params['temperature'],
                    top_p=self.api_params['top_p'],
                    max_tokens=self.api_params['max_tokens'],
                    frequency_penalty=self.api_params['frequency_penalty'],
                    presence_penalty=self.api_params['presence_penalty'],
                    stream=True
                )

            # 存储完整回答和推理过程
            reasoning_content = ""
            content = ""

            # 是否显示过推理过程标题
            reasoning_header_shown = False
            content_header_shown = False
            current_mode = None  # 用于跟踪当前是在处理推理内容还是回答内容

            # 处理流式响应
            for chunk in response:
                # 检查是否需要停止生成
                if self.stop_generation:
                    break

                delta = chunk.choices[0].delta

                # 处理推理内容
                if hasattr(delta, "reasoning_content") and delta.reasoning_content:
                    # 如果刚从回答内容切换到推理内容，添加分隔符
                    if current_mode != "reasoning":
                        current_mode = "reasoning"
                        # 如果这是第一次显示推理内容
                        if not reasoning_header_shown:
                            # 确保用户问题和推理过程之间有足够换行
                            self.append_history("\n\n")
                            self.update_history("深度思考", "")
                            reasoning_header_shown = True

                    reasoning_content += delta.reasoning_content
                    self.append_history_char(delta.reasoning_content)

                # 处理回答内容
                elif hasattr(delta, "content") and delta.content:
                    # 如果刚从推理内容切换到回答内容，添加分隔符
                    if current_mode != "content":
                        current_mode = "content"
                        # 如果这是第一次显示回答内容
                        if not content_header_shown:
                            # 如果已经显示了推理内容，先添加换行分隔
                            if reasoning_header_shown:
                                self.append_history("\n\n")
                            self.update_history("智能回答", "")
                            content_header_shown = True

                    content += delta.content
                    self.append_history_char(delta.content)

            # 在回答结束后添加额外的换行，为下一次提问做准备
            self.append_history("\n\n")

            # 将AI回答添加到对话历史
            self.messages.append({"role": "assistant", "content": content})

            # 记录AI完整回答到日志
            self.log_message("智能回答", content)

            # 如果有推理过程，也记录到日志
            if reasoning_content:
                self.log_message("深度思考", reasoning_content)

            self.set_status("准备就绪")

        except Exception as e:
            error_msg = f"API请求失败: {str(e)}"
            self.show_error(error_msg)
            self.log_message("系统错误", error_msg)
        finally:
            self.is_processing = False
            self.master.after(0, lambda: self.send_button.config(state='normal'))
            self.master.after(0, lambda: self.stop_button.config(state='disabled'))

    # ============== 对话历史/日志 ==============
    def stream_output(self, role, text):
        if not text:
            return
        self.log_message(role, text)
        self.update_history(role, "")
        for char in text:
            self.append_history_char(char)
            time.sleep(0.01)
        self.append_history("\n\n")

    def update_history(self, role, content):
        """更新对话历史UI，给不同角色添加不同样式"""
        self.history_text.configure(state='normal')

        # 为不同角色应用不同的样式
        if role == "You" or role == "用户":
            self.history_text.insert(END, f"{role}:\n", "role_user")
        elif role == "智能回答":
            self.history_text.insert(END, f"{role}:\n", "role_answer")
        elif role == "深度思考":
            self.history_text.insert(END, f"{role}:\n", "role_reasoning")
        else:
            self.history_text.insert(END, f"{role}:\n", "role_red")

        self.history_text.insert(END, f"{content}")
        self.history_text.configure(state='disabled')
        self.history_text.see(END)

    def log_message(self, role, text):
        """记录消息到日志文件"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        try:
            # 确保日志文件所在目录存在
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)

            # 如果文件不存在，创建一个空文件
            if not os.path.exists(self.log_file):
                with open(self.log_file, 'w', encoding='utf-8') as f:
                    f.write(f"# DeepSeek故障诊断助手对话记录 - 创建于 {timestamp}\n\n")

            # 以追加模式写入日志
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] {role}:\n{text}\n\n")
                f.flush()  # 确保立即写入磁盘

            self.status.set(f"对话已记录到 {self.log_file}")
        except Exception as e:
            error_msg = f"写入日志文件失败: {str(e)}"
            print(error_msg)
            self.status.set(error_msg)

            # 尝试写入到备用日志文件
            try:
                backup_log = "conversation_backup.txt"
                with open(backup_log, 'a', encoding='utf-8') as f:
                    f.write(f"[{timestamp}] {role}:\n{text}\n\n")
                self.status.set(f"对话已记录到备用文件 {backup_log}")
            except Exception as backup_error:
                self.status.set(f"备用日志也失败: {str(backup_error)}")

            # 确保用户知道发生了错误
            messagebox.showwarning("日志记录警告",
                                  f"无法将对话记录保存到 {self.log_file}\n\n错误信息: {str(e)}\n\n请检查文件权限或指定其他日志文件。")

    def append_history_char(self, char):
        self.history_text.configure(state='normal')
        self.history_text.insert(END, char)
        self.history_text.configure(state='disabled')
        self.history_text.see(END)

    def append_history(self, content):
        self.history_text.configure(state='normal')
        self.history_text.insert(END, content)
        self.history_text.configure(state='disabled')
        self.history_text.see(END)

    def set_status(self, text):
        self.master.after(0, self.status.set, text)

    def show_error(self, message):
        self.master.after(0, lambda: messagebox.showerror("错误", message))
        self.set_status("发生错误")

    # ============== 数据采集功能 ==============
    def start_data_acquisition(self):
        """开始数据采集"""
        try:
            # 检查DAQ硬件是否可用
            if not DAQ_AVAILABLE:
                messagebox.showerror("硬件错误", "数据采集硬件不可用！\n\n请确保：\n1. 已安装MCC DAQ驱动\n2. 硬件设备已正确连接\n3. 相关Python库已安装")
                return

            # 更新参数
            self.daq_params['sample_rate'] = int(self.daq_sample_rate_var.get())
            self.daq_params['num_points'] = int(self.daq_num_points_var.get())
            self.daq_params['device_id'] = self.daq_device_id_var.get()
            self.daq_params['board_num'] = int(self.daq_board_num_var.get())
            self.daq_params['channel'] = int(self.daq_channel_var.get())
            # 根据采样点数和采样频率计算采样时间
            self.daq_params['sample_time'] = self.daq_params['num_points'] / self.daq_params['sample_rate']

            # 验证参数合理性
            if self.daq_params['sample_rate'] <= 0 or self.daq_params['sample_rate'] > 100000:
                raise ValueError("采样频率应在1-100000 Hz范围内")

            if self.daq_params['num_points'] <= 0 or self.daq_params['num_points'] > 1000000:
                raise ValueError("采样点数应在1-1000000范围内")

            # 检查计算出的采样时间是否合理
            if self.daq_params['sample_time'] > 300:  # 最大5分钟
                raise ValueError(f"采样时间过长({self.daq_params['sample_time']:.2f}秒)，请减少采样点数或增加采样频率")

            if not self.daq_params['device_id'].strip():
                raise ValueError("设备ID不能为空")

            if self.daq_params['board_num'] < 0 or self.daq_params['board_num'] > 15:
                raise ValueError("板卡号应在0-15范围内")

            if self.daq_params['channel'] < 0 or self.daq_params['channel'] > 31:
                raise ValueError("物理通道应在0-31范围内")

            # 清空实时显示缓冲区
            self.clear_realtime_plot()
            # 重置实时数据缓冲区
            self.realtime_data_buffer = []

            # 开始实际数据采集
            self.daq_running = True
            self.daq_status_var.set("采集中...")
            self.daq_start_button.config(state='disabled')
            self.daq_stop_button.config(state='normal')

            # 启动采集线程
            import threading
            self.daq_thread = threading.Thread(target=self.data_acquisition_worker)
            self.daq_thread.daemon = True
            self.daq_thread.start()

            self.daq_info_label.config(text=f"正在采集数据... 板卡:{self.daq_params['board_num']} 通道:{self.daq_params['channel']} 频率:{self.daq_params['sample_rate']}Hz 点数:{self.daq_params['num_points']} 时间:{self.daq_params['sample_time']:.2f}s")

        except ValueError as e:
            messagebox.showerror("参数错误", f"请检查输入参数: {e}")
        except Exception as e:
            messagebox.showerror("采集错误", f"启动数据采集失败: {e}")

    def stop_data_acquisition(self):
        """停止数据采集"""
        self.daq_running = False
        self.daq_status_var.set("停止中...")

        if self.daq_thread and self.daq_thread.is_alive():
            self.daq_thread.join(timeout=2.0)

        self.daq_status_var.set("已停止")
        self.daq_start_button.config(state='normal')
        self.daq_stop_button.config(state='disabled')
        self.daq_info_label.config(text="数据采集已停止")

        # 清空实时显示
        self.clear_realtime_plot()



    def data_acquisition_worker(self):
        """数据采集工作线程 - 支持实时显示"""
        try:
            # 配置DAQ设备参数
            board_num = self.daq_params['board_num']
            channel = self.daq_params['channel']
            ul_range = self.daq_params['range']
            sample_rate = self.daq_params['sample_rate']
            num_points = self.daq_params['num_points']

            if not DAQ_AVAILABLE or ul is None:
                raise Exception("数据采集硬件不可用，无法进行数据采集")

            # 实时数据采集 - 分段采集以支持实时显示
            try:
                # 检查设备连接
                ul.get_board_name(board_num)

                # 分段采集参数
                chunk_size = min(1000, num_points // 10)  # 每次采集1000个点或总数的1/10
                if chunk_size < 100:
                    chunk_size = 100  # 最小100个点

                total_collected = 0
                all_signal_data = []

                # 更新状态
                self.master.after(0, lambda: self.daq_info_label.config(
                    text=f"开始实时采集 {num_points} 个数据点..."))

                while total_collected < num_points and self.daq_running:
                    # 计算本次采集的点数
                    current_chunk = min(chunk_size, num_points - total_collected)

                    # 分配内存缓冲区
                    memhandle = ul.win_buf_alloc(current_chunk)
                    if not memhandle:
                        raise RuntimeError("无法分配内存缓冲区")

                    try:
                        # 执行分段数据采集
                        ul.a_in_scan(board_num, channel, channel, current_chunk,
                                    sample_rate, ul_range, memhandle, options=0)

                        # 正确的数据转换方法
                        ctypes_array = ctypes.cast(memhandle, ctypes.POINTER(ctypes.c_ushort * current_chunk)).contents
                        raw_array = np.frombuffer(ctypes_array, dtype=np.uint16)

                        # 正确的电压转换公式：将16位ADC值转换为电压
                        chunk_data = (raw_array.astype(np.float32) / 65535.0) * 20.0 - 10.0

                        # 保存到总数据中
                        all_signal_data.extend(chunk_data)
                        total_collected += current_chunk

                        # 实时更新显示 - 显示当前分段数据
                        # 使用copy避免lambda闭包问题
                        chunk_copy = chunk_data.copy()
                        self.master.after(0, lambda: self.update_realtime_plot(chunk_copy, sample_rate))

                        # 更新进度
                        progress = (total_collected / num_points) * 100
                        self.master.after(0, lambda p=progress, c=total_collected:
                                        self.daq_info_label.config(
                                            text=f"实时采集中... {c}/{num_points} 点 ({p:.1f}%)"))

                        # 短暂延迟以便UI更新
                        import time
                        time.sleep(0.05)  # 50ms延迟

                    finally:
                        # 释放内存缓冲区
                        ul.win_buf_free(memhandle)

                # 检查采集是否完成
                if not self.daq_running:
                    self.master.after(0, lambda: self.daq_info_label.config(text="数据采集已取消"))
                    return

                # 转换为numpy数组
                signal_data = np.array(all_signal_data)
                actual_rate = sample_rate

            except Exception as e:
                raise Exception(f"无法连接到DAQ设备 (板卡 {board_num}): {str(e)}")

            # 检查采集是否被中断
            if not self.daq_running:
                self.master.after(0, lambda: self.daq_info_label.config(text="数据采集已取消"))
                return

            # 生成时间轴数据
            time_data = np.linspace(0, self.daq_params['sample_time'], num_points)

            # 数据质量检查
            if len(signal_data) != num_points:
                raise Exception(f"数据采集不完整: 期望 {num_points} 点，实际获得 {len(signal_data)} 点")

            # 检查信号是否有效（不全为零或常数）
            if np.all(signal_data == 0):
                raise Exception("采集到的信号全为零，请检查传感器连接和硬件配置")

            # 检查信号是否为常数（可能表示硬件问题）
            if np.std(signal_data) < 1e-6:
                raise Exception("采集到的信号为常数，请检查传感器连接和信号源")

            # 检查信号范围是否合理（±10V范围内）
            if np.max(np.abs(signal_data)) > 12.0:
                print(f"警告：信号幅值超出预期范围，最大值: {np.max(np.abs(signal_data)):.4f}V")

            # 存储数据
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.daq_data_buffer = {
                'timestamp': timestamp,
                'device_id': self.daq_params['device_id'],
                'sample_rate': actual_rate,
                'sample_time': self.daq_params['sample_time'],
                'num_points': num_points,
                'time': time_data,
                'signal': signal_data,
                'bearing_params': {
                    'model': self.daq_params['bearing_model'],
                    'rpm': self.daq_params['rpm'],
                    'ball_count': self.daq_params['ball_count']
                }
            }

            # 更新UI - 采集成功
            self.master.after(0, lambda: self.daq_status_var.set("采集完成"))
            self.master.after(0, lambda: self.daq_info_label.config(
                text=f"✅ 数据采集完成 - {num_points}个采样点，实际频率:{actual_rate:.1f}Hz\n信号范围: {np.min(signal_data):.4f}V ~ {np.max(signal_data):.4f}V\n信号标准差: {np.std(signal_data):.6f}V"))

        except Exception as e:
            error_msg = f"数据采集失败: {str(e)}"
            self.master.after(0, lambda: messagebox.showerror("采集错误", error_msg))
            self.master.after(0, lambda: self.daq_status_var.set("采集失败"))
            self.master.after(0, lambda: self.daq_info_label.config(text=f"❌ {error_msg}"))
        finally:
            # 确保清理状态
            self.daq_running = False
            self.master.after(0, lambda: self.daq_start_button.config(state='normal'))
            self.master.after(0, lambda: self.daq_stop_button.config(state='disabled'))

    def save_daq_data(self):
        """保存采集的数据 - 支持自定义保存路径"""
        if not self.daq_data_buffer:
            messagebox.showwarning("无数据", "没有可保存的数据，请先进行数据采集")
            return

        try:
            # 获取当前参数值
            bearing_id = self.daq_bearing_id_var.get()
            bearing_model = self.daq_bearing_model_var.get()
            rpm = self.daq_rpm_var.get()
            sample_rate = self.daq_sample_rate_var.get()
            num_points = self.daq_num_points_var.get()
            ball_count = self.daq_ball_count_var.get()
            ball_diameter = self.daq_ball_diameter_var.get()
            pitch_diameter = self.daq_pitch_diameter_var.get()
            contact_angle = self.daq_contact_angle_var.get()

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 生成标准化文件名：包含采样点数信息
            excel_filename = (f"Vibration_{bearing_id}_{bearing_model}_{rpm}rpm_{sample_rate}Hz_"
                             f"{num_points}pts_{ball_count}balls_{ball_diameter}mm_{pitch_diameter}mm_"
                             f"{contact_angle}deg_{timestamp}.xlsx")

            # 使用文件对话框让用户选择保存路径
            excel_filepath = filedialog.asksaveasfilename(
                title="保存振动数据",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel文件", "*.xlsx"),
                    ("所有文件", "*.*")
                ],
                initialfile=excel_filename,
                initialdir=self.daq_save_path  # 默认目录为data文件夹
            )

            # 如果用户取消了保存
            if not excel_filepath:
                return

            # 创建DataFrame
            df = pd.DataFrame({
                'Time(s)': self.daq_data_buffer['time'],
                'Signal(V)': self.daq_data_buffer['signal']
            })

            with pd.ExcelWriter(excel_filepath, engine='openpyxl') as writer:
                # 原始数据工作表
                df.to_excel(writer, sheet_name='RawData', index=False)

                # 参数信息工作表
                params_df = pd.DataFrame({
                    'Parameter': ['Device ID', 'Bearing ID', 'Bearing Model', 'Sample Rate (Hz)', 'Sample Time (s)',
                                 'Number of Points', 'RPM', 'Ball Count', 'Ball Diameter (mm)',
                                 'Pitch Diameter (mm)', 'Contact Angle (deg)', 'Timestamp'],
                    'Value': [self.daq_data_buffer['device_id'], bearing_id, bearing_model,
                             self.daq_data_buffer['sample_rate'], self.daq_data_buffer['sample_time'],
                             self.daq_data_buffer['num_points'], rpm, ball_count, ball_diameter,
                             pitch_diameter, contact_angle, timestamp]
                })
                params_df.to_excel(writer, sheet_name='Parameters', index=False)

                # 统计信息工作表
                signal_data = self.daq_data_buffer['signal']
                stats_df = pd.DataFrame({
                    'Statistic': ['Mean', 'Standard Deviation', 'Peak-to-Peak', 'RMS', 'Kurtosis', 'Skewness'],
                    'Value': [
                        np.mean(signal_data),
                        np.std(signal_data),
                        np.ptp(signal_data),
                        np.sqrt(np.mean(signal_data**2)),
                        scipy.stats.kurtosis(signal_data) if hasattr(scipy, 'stats') else 0,
                        scipy.stats.skew(signal_data) if hasattr(scipy, 'stats') else 0
                    ]
                })
                stats_df.to_excel(writer, sheet_name='Statistics', index=False)

                # 故障特征频率工作表
                try:
                    # 计算故障特征频率
                    rpm_val = float(rpm)
                    ball_count_val = int(ball_count)
                    ball_diameter_val = float(ball_diameter)
                    pitch_diameter_val = float(pitch_diameter)
                    contact_angle_val = float(contact_angle)

                    shaft_freq = rpm_val / 60.0  # 转轴频率 Hz
                    contact_angle_rad = contact_angle_val * np.pi / 180  # 转换为弧度

                    # 计算故障特征频率
                    bpfi = (ball_count_val / 2) * (1 + ball_diameter_val / pitch_diameter_val * np.cos(contact_angle_rad)) * shaft_freq
                    bpfo = (ball_count_val / 2) * (1 - ball_diameter_val / pitch_diameter_val * np.cos(contact_angle_rad)) * shaft_freq
                    bsf = (pitch_diameter_val / ball_diameter_val) * (1 - (ball_diameter_val / pitch_diameter_val * np.cos(contact_angle_rad))**2) * shaft_freq
                    ftf = (1 / 2) * (1 - ball_diameter_val / pitch_diameter_val * np.cos(contact_angle_rad)) * shaft_freq

                    fault_freqs_df = pd.DataFrame({
                        'Fault Type': ['BPFI (Inner Race)', 'BPFO (Outer Race)', 'BSF (Ball Spin)', 'FTF (Cage)'],
                        'Frequency (Hz)': [bpfi, bpfo, bsf, ftf],
                        'Description': ['Ball Pass Frequency Inner', 'Ball Pass Frequency Outer',
                                       'Ball Spin Frequency', 'Fundamental Train Frequency']
                    })
                    fault_freqs_df.to_excel(writer, sheet_name='FaultFrequencies', index=False)
                except Exception as e:
                    print(f"计算故障特征频率失败: {e}")

            # 获取保存的文件名和目录
            saved_filename = os.path.basename(excel_filepath)
            saved_directory = os.path.dirname(excel_filepath)

            messagebox.showinfo("保存成功",
                               f"数据已保存为Excel文件:\n{saved_filename}\n\n"
                               f"保存路径:\n{saved_directory}")

            self.daq_info_label.config(text=f"✅ 数据已保存: {saved_filename}")

            # 更新状态栏
            self.status.set(f"数据已保存至 {saved_filename}")

        except Exception as e:
            messagebox.showerror("保存失败", f"保存数据失败: {e}")

    def preview_daq_excel(self):
        """预览Excel文件"""
        if not self.daq_data_buffer:
            messagebox.showwarning("无数据", "没有可预览的数据，请先进行数据采集")
            return

        try:
            # 创建预览窗口
            preview_window = Toplevel(self.master)
            preview_window.title("📊 Excel数据预览")
            preview_window.geometry("800x600")
            preview_window.configure(bg=self.colors['background'])

            # 标题
            title_frame = Frame(preview_window, bg=self.colors['primary'], height=50)
            title_frame.pack(fill=X)
            title_frame.pack_propagate(False)

            Label(title_frame, text="📊 数据采集Excel预览",
                  font=("微软雅黑", 14, "bold"),
                  fg='white', bg=self.colors['primary']).pack(expand=True)

            # 创建Notebook用于多标签页
            notebook = ttk.Notebook(preview_window)
            notebook.pack(fill=BOTH, expand=True, padx=10, pady=10)

            # 原始数据标签页
            raw_frame = Frame(notebook, bg='white')
            notebook.add(raw_frame, text="原始数据")

            # 创建表格显示原始数据
            tree_frame = Frame(raw_frame, bg='white')
            tree_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

            tree = ttk.Treeview(tree_frame, columns=('Time', 'Signal'), show='headings', height=15)
            tree.heading('Time', text='时间(s)')
            tree.heading('Signal', text='信号(V)')
            tree.column('Time', width=200, anchor='center')
            tree.column('Signal', width=200, anchor='center')

            # 添加滚动条
            scrollbar = ttk.Scrollbar(tree_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            tree.pack(side=LEFT, fill=BOTH, expand=True)
            scrollbar.pack(side=RIGHT, fill=Y)

            # 插入数据（显示前100个点）
            time_data = self.daq_data_buffer['time']
            signal_data = self.daq_data_buffer['signal']

            display_points = min(100, len(time_data))
            for i in range(display_points):
                tree.insert('', 'end', values=(f"{time_data[i]:.6f}", f"{signal_data[i]:.6f}"))

            if len(time_data) > display_points:
                tree.insert('', 'end', values=("...", "..."))
                tree.insert('', 'end', values=(f"总共 {len(time_data)} 个数据点", ""))

            # 参数信息标签页
            params_frame = Frame(notebook, bg='white')
            notebook.add(params_frame, text="参数信息")

            params_tree = ttk.Treeview(params_frame, columns=('Parameter', 'Value'), show='headings', height=10)
            params_tree.heading('Parameter', text='参数')
            params_tree.heading('Value', text='值')
            params_tree.column('Parameter', width=300, anchor='w')
            params_tree.column('Value', width=300, anchor='w')

            params_tree.pack(fill=BOTH, expand=True, padx=10, pady=10)

            # 插入参数信息
            params_info = [
                ('设备ID', self.daq_data_buffer['device_id']),
                ('采样频率 (Hz)', self.daq_data_buffer['sample_rate']),
                ('采样时间 (s)', self.daq_data_buffer['sample_time']),
                ('采样点数', self.daq_data_buffer['num_points']),
                ('轴承型号', self.daq_data_buffer['bearing_params']['model']),
                ('转速 (RPM)', self.daq_data_buffer['bearing_params']['rpm']),
                ('采集时间', self.daq_data_buffer['timestamp'])
            ]

            for param, value in params_info:
                params_tree.insert('', 'end', values=(param, value))

            # 统计信息标签页
            stats_frame = Frame(notebook, bg='white')
            notebook.add(stats_frame, text="统计信息")

            stats_tree = ttk.Treeview(stats_frame, columns=('Statistic', 'Value'), show='headings', height=10)
            stats_tree.heading('Statistic', text='统计量')
            stats_tree.heading('Value', text='数值')
            stats_tree.column('Statistic', width=300, anchor='w')
            stats_tree.column('Value', width=300, anchor='w')

            stats_tree.pack(fill=BOTH, expand=True, padx=10, pady=10)

            # 计算并插入统计信息
            signal_data = self.daq_data_buffer['signal']
            stats_info = [
                ('均值 (Mean)', f"{np.mean(signal_data):.6f}"),
                ('标准差 (Std Dev)', f"{np.std(signal_data):.6f}"),
                ('峰峰值 (Peak-to-Peak)', f"{np.ptp(signal_data):.6f}"),
                ('有效值 (RMS)', f"{np.sqrt(np.mean(signal_data**2)):.6f}"),
                ('最大值 (Maximum)', f"{np.max(signal_data):.6f}"),
                ('最小值 (Minimum)', f"{np.min(signal_data):.6f}"),
                ('方差 (Variance)', f"{np.var(signal_data):.6f}")
            ]

            # 如果scipy.stats可用，添加更多统计量
            try:
                import scipy.stats
                stats_info.extend([
                    ('峭度 (Kurtosis)', f"{scipy.stats.kurtosis(signal_data):.6f}"),
                    ('偏度 (Skewness)', f"{scipy.stats.skew(signal_data):.6f}")
                ])
            except:
                pass

            for stat, value in stats_info:
                stats_tree.insert('', 'end', values=(stat, value))

        except Exception as e:
            messagebox.showerror("预览失败", f"创建Excel预览失败: {e}")

    def test_daq_connection(self):
        """测试DAQ硬件连接"""
        try:
            # 检查DAQ库是否可用
            if not DAQ_AVAILABLE:
                messagebox.showerror("硬件测试",
                    "❌ 数据采集库不可用！\n\n"
                    "请确保：\n"
                    "1. 已安装MCC DAQ驱动程序\n"
                    "2. 已安装mcculw Python库\n"
                    "3. 硬件设备已正确连接\n\n"
                    "安装命令：pip install mcculw")
                return

            # 获取当前设置的板卡号
            board_num = int(self.daq_board_num_var.get())
            channel = int(self.daq_channel_var.get())

            # 创建测试结果窗口
            test_window = Toplevel(self.master)
            test_window.title("🔧 DAQ硬件连接测试")
            test_window.geometry("600x400")
            test_window.configure(bg='white')

            # 标题
            title_frame = Frame(test_window, bg=self.colors['primary'], height=50)
            title_frame.pack(fill=X)
            title_frame.pack_propagate(False)

            Label(title_frame, text="🔧 DAQ硬件连接测试",
                  font=("微软雅黑", 14, "bold"),
                  fg='white', bg=self.colors['primary']).pack(expand=True)

            # 测试结果显示区域
            result_frame = Frame(test_window, bg='white')
            result_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            result_text = scrolledtext.ScrolledText(result_frame, wrap=WORD,
                                                   font=("Consolas", 10),
                                                   height=15, width=70)
            result_text.pack(fill=BOTH, expand=True)

            def run_test():
                """执行测试"""
                result_text.delete(1.0, END)
                result_text.insert(END, "开始DAQ硬件连接测试...\n\n")
                result_text.update()

                try:
                    # 测试1：检查板卡是否存在
                    result_text.insert(END, f"测试1：检查板卡 {board_num} 是否存在...\n")
                    result_text.update()

                    board_name = ul.get_board_name(board_num)
                    result_text.insert(END, f"✅ 板卡 {board_num} 检测成功：{board_name}\n\n")
                    result_text.update()

                    # 测试2：检查通道配置
                    result_text.insert(END, f"测试2：检查通道 {channel} 配置...\n")
                    result_text.update()

                    # 尝试读取单个数据点
                    ul_range = self.daq_params['range']
                    test_value = ul.a_in(board_num, channel, ul_range)
                    voltage = ul.to_eng_units(board_num, ul_range, test_value)

                    result_text.insert(END, f"✅ 通道 {channel} 测试成功\n")
                    result_text.insert(END, f"   原始值: {test_value}\n")
                    result_text.insert(END, f"   电压值: {voltage:.6f} V\n\n")
                    result_text.update()

                    # 测试3：小批量数据采集测试
                    result_text.insert(END, "测试3：小批量数据采集测试（100个点）...\n")
                    result_text.update()

                    test_points = 100
                    test_rate = 1000  # 1kHz测试采样率

                    # 分配内存缓冲区
                    memhandle = ul.win_buf_alloc(test_points)
                    if not memhandle:
                        raise RuntimeError("无法分配内存缓冲区")

                    try:
                        # 执行小批量采集
                        ul.a_in_scan(board_num, channel, channel, test_points,
                                    test_rate, ul_range, memhandle, options=0)

                        # 转换数据
                        ctypes_array = ctypes.cast(memhandle, ctypes.POINTER(ctypes.c_ushort * test_points)).contents
                        raw_array = np.frombuffer(ctypes_array, dtype=np.uint16)
                        voltage_array = (raw_array.astype(np.float32) / 65535.0) * 20.0 - 10.0

                        # 分析测试数据
                        mean_val = np.mean(voltage_array)
                        std_val = np.std(voltage_array)
                        min_val = np.min(voltage_array)
                        max_val = np.max(voltage_array)

                        result_text.insert(END, f"✅ 小批量采集测试成功\n")
                        result_text.insert(END, f"   采集点数: {len(voltage_array)}\n")
                        result_text.insert(END, f"   信号统计:\n")
                        result_text.insert(END, f"     均值: {mean_val:.6f} V\n")
                        result_text.insert(END, f"     标准差: {std_val:.6f} V\n")
                        result_text.insert(END, f"     范围: {min_val:.6f} ~ {max_val:.6f} V\n\n")
                        result_text.update()

                        # 数据质量检查
                        if np.all(voltage_array == 0):
                            result_text.insert(END, "⚠️  警告：采集到的信号全为零，请检查传感器连接\n\n")
                        elif std_val < 1e-6:
                            result_text.insert(END, "⚠️  警告：信号为常数，可能存在硬件问题\n\n")
                        else:
                            result_text.insert(END, "✅ 数据质量检查通过\n\n")

                    finally:
                        ul.win_buf_free(memhandle)

                    # 测试总结
                    result_text.insert(END, "=" * 50 + "\n")
                    result_text.insert(END, "🎉 DAQ硬件连接测试完成！\n\n")
                    result_text.insert(END, "测试结果：\n")
                    result_text.insert(END, f"✅ 板卡 {board_num} 连接正常\n")
                    result_text.insert(END, f"✅ 通道 {channel} 工作正常\n")
                    result_text.insert(END, f"✅ 数据采集功能正常\n\n")
                    result_text.insert(END, "您现在可以开始正式的数据采集了！\n")

                except Exception as e:
                    result_text.insert(END, f"❌ 测试失败：{str(e)}\n\n")
                    result_text.insert(END, "可能的解决方案：\n")
                    result_text.insert(END, "1. 检查DAQ设备是否正确连接\n")
                    result_text.insert(END, "2. 检查板卡号和通道号设置\n")
                    result_text.insert(END, "3. 重新安装MCC DAQ驱动程序\n")
                    result_text.insert(END, "4. 检查设备管理器中的硬件状态\n")

                result_text.see(END)

            # 按钮区域
            button_frame = Frame(test_window, bg='white')
            button_frame.pack(fill=X, padx=20, pady=10)

            Button(button_frame, text="🚀 开始测试",
                   font=("微软雅黑", 10, "bold"),
                   bg=self.colors['success'], fg='white',
                   relief='flat', padx=20, pady=5,
                   command=run_test).pack(side=LEFT, padx=(0, 10))

            Button(button_frame, text="关闭",
                   font=("微软雅黑", 10),
                   bg=self.colors['surface'], fg=self.colors['text_primary'],
                   relief='flat', padx=20, pady=5,
                   command=test_window.destroy).pack(side=LEFT)

            # 自动运行测试
            test_window.after(500, run_test)

        except Exception as e:
            messagebox.showerror("测试错误", f"无法启动DAQ连接测试：{str(e)}")


# ============== 程序入口 ==============
if __name__ == "__main__":
    root = Tk()
    try:
        app = DeepSeekGUI(root)
        root.mainloop()
    except Exception as e:
        messagebox.showerror("启动失败", f"程序无法启动: {str(e)}")