#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习模型定义模块
包含1DCNN、2DCNN等故障诊断模型
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score
import seaborn as sns

class VibrationDataset(Dataset):
    """振动数据集类"""

    def __init__(self, X, y, transform=None):
        """
        Args:
            X: 振动数据 (samples, length, channels)
            y: 标签 (samples,)
            transform: 数据变换函数
        """
        self.X = torch.FloatTensor(X)
        self.y = torch.LongTensor(y)
        self.transform = transform

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        sample = self.X[idx]
        label = self.y[idx]

        # 确保样本是正确的形状 (channels, length)
        if sample.dim() == 2 and sample.shape[1] == 1:
            # 如果是 (length, 1)，转换为 (1, length)
            sample = sample.transpose(0, 1)
        elif sample.dim() == 1:
            # 如果是 (length,)，添加通道维度
            sample = sample.unsqueeze(0)

        if self.transform:
            sample = self.transform(sample)

        return sample, label

class CNN1D(nn.Module):
    """1D卷积神经网络用于振动信号故障诊断"""

    def __init__(self, input_length=1024, num_classes=4, dropout_rate=0.5):
        """
        Args:
            input_length: 输入信号长度
            num_classes: 故障类别数
            dropout_rate: Dropout比率
        """
        super(CNN1D, self).__init__()

        self.input_length = input_length
        self.num_classes = num_classes

        # 卷积层
        self.conv1 = nn.Conv1d(1, 32, kernel_size=64, stride=1, padding=32)
        self.bn1 = nn.BatchNorm1d(32)
        self.pool1 = nn.MaxPool1d(kernel_size=2, stride=2)

        self.conv2 = nn.Conv1d(32, 64, kernel_size=32, stride=1, padding=16)
        self.bn2 = nn.BatchNorm1d(64)
        self.pool2 = nn.MaxPool1d(kernel_size=2, stride=2)

        self.conv3 = nn.Conv1d(64, 128, kernel_size=16, stride=1, padding=8)
        self.bn3 = nn.BatchNorm1d(128)
        self.pool3 = nn.MaxPool1d(kernel_size=2, stride=2)

        # 计算展平后的特征维度
        self.feature_dim = self._get_conv_output_size()

        # 全连接层
        self.fc1 = nn.Linear(self.feature_dim, 256)
        self.dropout1 = nn.Dropout(dropout_rate)
        self.fc2 = nn.Linear(256, 128)
        self.dropout2 = nn.Dropout(dropout_rate)
        self.fc3 = nn.Linear(128, num_classes)

    def _get_conv_output_size(self):
        """计算卷积层输出的特征维度"""
        with torch.no_grad():
            x = torch.randn(1, 1, self.input_length)
            x = self.pool1(F.relu(self.bn1(self.conv1(x))))
            x = self.pool2(F.relu(self.bn2(self.conv2(x))))
            x = self.pool3(F.relu(self.bn3(self.conv3(x))))
            return x.numel()

    def forward(self, x):
        """前向传播"""
        # 卷积层
        x = self.pool1(F.relu(self.bn1(self.conv1(x))))
        x = self.pool2(F.relu(self.bn2(self.conv2(x))))
        x = self.pool3(F.relu(self.bn3(self.conv3(x))))

        # 展平
        x = x.view(x.size(0), -1)

        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = self.fc3(x)

        return x

class CNN2D(nn.Module):
    """2D卷积神经网络用于时频图故障诊断"""

    def __init__(self, input_height=64, input_width=64, num_classes=4, dropout_rate=0.5):
        """
        Args:
            input_height: 输入图像高度
            input_width: 输入图像宽度
            num_classes: 故障类别数
            dropout_rate: Dropout比率
        """
        super(CNN2D, self).__init__()

        self.input_height = input_height
        self.input_width = input_width
        self.num_classes = num_classes

        # 卷积层
        self.conv1 = nn.Conv2d(1, 32, kernel_size=3, stride=1, padding=1)
        self.bn1 = nn.BatchNorm2d(32)
        self.pool1 = nn.MaxPool2d(kernel_size=2, stride=2)

        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1)
        self.bn2 = nn.BatchNorm2d(64)
        self.pool2 = nn.MaxPool2d(kernel_size=2, stride=2)

        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1)
        self.bn3 = nn.BatchNorm2d(128)
        self.pool3 = nn.MaxPool2d(kernel_size=2, stride=2)

        # 计算展平后的特征维度
        self.feature_dim = self._get_conv_output_size()

        # 全连接层
        self.fc1 = nn.Linear(self.feature_dim, 256)
        self.dropout1 = nn.Dropout(dropout_rate)
        self.fc2 = nn.Linear(256, 128)
        self.dropout2 = nn.Dropout(dropout_rate)
        self.fc3 = nn.Linear(128, num_classes)

    def _get_conv_output_size(self):
        """计算卷积层输出的特征维度"""
        with torch.no_grad():
            x = torch.randn(1, 1, self.input_height, self.input_width)
            x = self.pool1(F.relu(self.bn1(self.conv1(x))))
            x = self.pool2(F.relu(self.bn2(self.conv2(x))))
            x = self.pool3(F.relu(self.bn3(self.conv3(x))))
            return x.numel()

    def forward(self, x):
        """前向传播"""
        # 卷积层
        x = self.pool1(F.relu(self.bn1(self.conv1(x))))
        x = self.pool2(F.relu(self.bn2(self.conv2(x))))
        x = self.pool3(F.relu(self.bn3(self.conv3(x))))

        # 展平
        x = x.view(x.size(0), -1)

        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.dropout1(x)
        x = F.relu(self.fc2(x))
        x = self.dropout2(x)
        x = self.fc3(x)

        return x

class ModelTrainer:
    """模型训练器"""

    def __init__(self, model, device='cpu'):
        """
        Args:
            model: 要训练的模型
            device: 训练设备 ('cpu' 或 'cuda')
        """
        self.model = model
        self.device = device
        self.model.to(device)

        self.train_losses = []
        self.train_accuracies = []
        self.val_losses = []
        self.val_accuracies = []

    def train_epoch(self, train_loader, optimizer, criterion):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)

            optimizer.zero_grad()
            output = self.model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

        avg_loss = total_loss / len(train_loader)
        accuracy = 100. * correct / total

        return avg_loss, accuracy

    def validate_epoch(self, val_loader, criterion):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        correct = 0
        total = 0

        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                loss = criterion(output, target)

                total_loss += loss.item()
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)

        avg_loss = total_loss / len(val_loader)
        accuracy = 100. * correct / total

        return avg_loss, accuracy

    def train(self, train_loader, val_loader, epochs, learning_rate=0.001,
              weight_decay=1e-4, patience=10, progress_callback=None):
        """
        训练模型

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练轮数
            learning_rate: 学习率
            weight_decay: 权重衰减
            patience: 早停耐心值
            progress_callback: 进度回调函数
        """
        optimizer = torch.optim.Adam(self.model.parameters(),
                                   lr=learning_rate, weight_decay=weight_decay)
        criterion = nn.CrossEntropyLoss()
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5)

        best_val_acc = 0
        patience_counter = 0

        for epoch in range(epochs):
            # 训练
            train_loss, train_acc = self.train_epoch(train_loader, optimizer, criterion)

            # 验证
            val_loss, val_acc = self.validate_epoch(val_loader, criterion)

            # 记录历史
            self.train_losses.append(train_loss)
            self.train_accuracies.append(train_acc)
            self.val_losses.append(val_loss)
            self.val_accuracies.append(val_acc)

            # 学习率调度
            scheduler.step(val_loss)

            # 早停检查
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_model.pth')
            else:
                patience_counter += 1

            # 进度回调
            if progress_callback:
                progress_callback(epoch + 1, epochs, train_loss, train_acc, val_loss, val_acc)

            # 早停
            if patience_counter >= patience:
                print(f"Early stopping at epoch {epoch + 1}")
                break

        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_model.pth'))

        return {
            'train_losses': self.train_losses,
            'train_accuracies': self.train_accuracies,
            'val_losses': self.val_losses,
            'val_accuracies': self.val_accuracies,
            'best_val_accuracy': best_val_acc
        }

    def evaluate(self, test_loader, class_names):
        """评估模型"""
        self.model.eval()
        all_preds = []
        all_targets = []

        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                pred = output.argmax(dim=1)

                all_preds.extend(pred.cpu().numpy())
                all_targets.extend(target.cpu().numpy())

        # 计算指标
        accuracy = accuracy_score(all_targets, all_preds)
        report = classification_report(all_targets, all_preds,
                                     target_names=class_names, output_dict=True)
        cm = confusion_matrix(all_targets, all_preds)

        return {
            'accuracy': accuracy,
            'classification_report': report,
            'confusion_matrix': cm,
            'predictions': all_preds,
            'targets': all_targets
        }

    def predict(self, data_loader):
        """预测"""
        self.model.eval()
        predictions = []
        probabilities = []

        with torch.no_grad():
            for data, _ in data_loader:
                data = data.to(self.device)
                output = self.model(data)
                probs = F.softmax(output, dim=1)
                pred = output.argmax(dim=1)

                predictions.extend(pred.cpu().numpy())
                probabilities.extend(probs.cpu().numpy())

        return np.array(predictions), np.array(probabilities)

def plot_training_history(history, save_path=None):
    """绘制训练历史"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))  # 减小图形尺寸

    # 损失曲线
    ax1.plot(history['train_losses'], label='训练损失', color='blue', linewidth=1.5, marker='o', markersize=3)
    ax1.plot(history['val_losses'], label='验证损失', color='red', linewidth=1.5, marker='s', markersize=3)
    ax1.set_title('模型损失变化', fontsize=11, fontweight='bold', fontfamily='Microsoft YaHei')  # 减小字体
    ax1.set_xlabel('训练轮次', fontsize=9, fontfamily='Microsoft YaHei')  # 减小字体
    ax1.set_ylabel('损失值', fontsize=9, fontfamily='Microsoft YaHei')  # 减小字体
    ax1.legend(fontsize=8)  # 减小图例字体
    ax1.tick_params(labelsize=8)  # 减小刻度标签字体
    ax1.grid(True, alpha=0.3)

    # 准确率曲线
    ax2.plot(history['train_accuracies'], label='训练准确率', color='blue', linewidth=1.5, marker='o', markersize=3)
    ax2.plot(history['val_accuracies'], label='验证准确率', color='red', linewidth=1.5, marker='s', markersize=3)
    ax2.set_title('模型准确率变化', fontsize=11, fontweight='bold', fontfamily='Microsoft YaHei')  # 减小字体
    ax2.set_xlabel('训练轮次', fontsize=9, fontfamily='Microsoft YaHei')  # 减小字体
    ax2.set_ylabel('准确率 (%)', fontsize=9, fontfamily='Microsoft YaHei')  # 减小字体
    ax2.legend(fontsize=8)  # 减小图例字体
    ax2.tick_params(labelsize=8)  # 减小刻度标签字体
    ax2.grid(True, alpha=0.3)

    plt.tight_layout(pad=1.0)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

def plot_confusion_matrix(cm, class_names, save_path=None):
    """绘制混淆矩阵"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 故障类型中文映射
    fault_type_chinese = {
        # 轴承故障类型
        'normal': '正常状态',
        'ball_fault': '滚动体故障',
        'inner_race_fault': '内圈故障',
        'outer_race_fault': '外圈故障',
        'IR': '内圈故障',
        'OR': '外圈故障',
        'B': '滚动体故障',
        'N': '正常状态',

        # 风机故障类型
        'rotor_unbalance': '转子不平衡',
        'rotor_misalignment': '转子不对中',
        'blade_fault': '叶片故障',
        'RU': '转子不平衡',
        'RM': '转子不对中',
        'BF': '叶片故障'
    }

    # 转换为中文标签
    chinese_labels = [fault_type_chinese.get(name, name) for name in class_names]

    plt.figure(figsize=(8, 6))  # 减小图形尺寸
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=chinese_labels, yticklabels=chinese_labels,
                annot_kws={'fontsize': 10})  # 减小注释字体
    plt.title('故障诊断混淆矩阵', fontsize=12, fontweight='bold', fontfamily='Microsoft YaHei')  # 减小标题字体
    plt.xlabel('预测故障类型', fontsize=10, fontfamily='Microsoft YaHei')  # 减小轴标签字体
    plt.ylabel('真实故障类型', fontsize=10, fontfamily='Microsoft YaHei')
    plt.tick_params(labelsize=9)  # 减小刻度标签字体

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return plt.gcf()
