#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
风机故障诊断功能演示脚本

演示更新后的风机故障诊断功能，只包含三种故障类型：
1. 转子不平衡
2. 转子不对中  
3. 叶片故障
"""

import sys
import os
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_wind_turbine_fault_diagnosis():
    """演示风机故障诊断功能"""
    print("=" * 60)
    print("风机故障诊断系统演示")
    print("=" * 60)
    
    print("\n🌪️ 支持的风机故障类型：")
    print("1. 转子不平衡 (Rotor Unbalance)")
    print("2. 转子不对中 (Rotor Misalignment)")
    print("3. 叶片故障 (Blade Fault)")
    
    print("\n📊 特征频率计算示例：")
    print("-" * 40)
    
    # 示例风机参数
    rpm = 1800  # 转速
    blade_count = 3  # 叶片数
    
    # 计算特征频率
    rotor_freq = rpm / 60.0  # 转子转频
    bpf = rotor_freq * blade_count  # 叶片通过频率
    
    print(f"风机参数：")
    print(f"  转速: {rpm} RPM")
    print(f"  叶片数: {blade_count}")
    print(f"  转子转频: {rotor_freq:.2f} Hz")
    print(f"  叶片通过频率: {bpf:.2f} Hz")
    
    print(f"\n故障特征频率：")
    print(f"  转子不平衡1X: {rotor_freq:.2f} Hz")
    print(f"  转子不平衡2X: {rotor_freq*2:.2f} Hz")
    print(f"  转子不对中2X: {rotor_freq*2:.2f} Hz")
    print(f"  转子不对中4X: {rotor_freq*4:.2f} Hz")
    print(f"  叶片故障1X: {bpf:.2f} Hz")
    print(f"  叶片故障2X: {bpf*2:.2f} Hz")
    
    print("\n🔍 故障诊断原理：")
    print("-" * 40)
    print("• 转子不平衡：主要表现为1X转频及其谐波")
    print("  - 原因：叶片积灰、叶片损伤、制造误差")
    print("  - 特征：径向振动为主，1X转频幅值突出")
    
    print("\n• 转子不对中：主要表现为2X转频及其谐波")
    print("  - 原因：基础沉降、热膨胀、安装误差")
    print("  - 特征：轴向振动明显，2X转频幅值突出")
    
    print("\n• 叶片故障：表现为叶片通过频率及其谐波")
    print("  - 原因：叶片裂纹、积冰、疲劳损伤")
    print("  - 特征：BPF及其谐波幅值增大")

def demo_fault_signal_analysis():
    """演示故障信号分析"""
    print("\n" + "=" * 60)
    print("故障信号分析演示")
    print("=" * 60)
    
    # 生成包含故障特征的模拟信号
    fs = 1000  # 采样频率
    t = np.linspace(0, 5, fs * 5)  # 5秒信号
    
    # 风机参数
    rpm = 1800
    rotor_freq = rpm / 60.0  # 30 Hz
    bpf = rotor_freq * 3  # 90 Hz (3叶片)
    
    print(f"\n📈 信号参数：")
    print(f"  采样频率: {fs} Hz")
    print(f"  信号长度: {len(t)} 点 ({len(t)/fs} 秒)")
    print(f"  转子转频: {rotor_freq:.2f} Hz")
    print(f"  叶片通过频率: {bpf:.2f} Hz")
    
    # 生成不同故障类型的信号
    fault_types = {
        '正常状态': {
            'signal': 0.5 * np.sin(2 * np.pi * rotor_freq * t) + 0.1 * np.random.normal(0, 1, len(t)),
            'description': '只有基频成分，振动水平较低'
        },
        '转子不平衡': {
            'signal': (0.8 * np.sin(2 * np.pi * rotor_freq * t) + 
                      0.3 * np.sin(2 * np.pi * 2 * rotor_freq * t) + 
                      0.1 * np.random.normal(0, 1, len(t))),
            'description': '1X转频幅值增大，2X转频成分出现'
        },
        '转子不对中': {
            'signal': (0.4 * np.sin(2 * np.pi * rotor_freq * t) + 
                      0.6 * np.sin(2 * np.pi * 2 * rotor_freq * t) + 
                      0.2 * np.sin(2 * np.pi * 4 * rotor_freq * t) + 
                      0.1 * np.random.normal(0, 1, len(t))),
            'description': '2X转频成分突出，4X转频成分明显'
        },
        '叶片故障': {
            'signal': (0.5 * np.sin(2 * np.pi * rotor_freq * t) + 
                      0.4 * np.sin(2 * np.pi * bpf * t) + 
                      0.2 * np.sin(2 * np.pi * 2 * bpf * t) + 
                      0.1 * np.random.normal(0, 1, len(t))),
            'description': '叶片通过频率及其谐波幅值增大'
        }
    }
    
    print(f"\n🔬 故障信号特征分析：")
    print("-" * 50)
    
    for fault_name, fault_data in fault_types.items():
        signal = fault_data['signal']
        description = fault_data['description']
        
        # 计算信号统计特征
        rms = np.sqrt(np.mean(signal**2))
        peak = np.max(np.abs(signal))
        crest_factor = peak / rms
        
        # 简单频谱分析
        fft_signal = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/fs)
        positive_freqs = freqs[:len(freqs)//2]
        positive_fft = np.abs(fft_signal[:len(fft_signal)//2])
        
        # 找到主要频率成分
        peak_indices = np.argsort(positive_fft)[-3:]  # 前3个最大峰值
        main_freqs = []
        for idx in reversed(peak_indices):
            freq = positive_freqs[idx]
            amplitude = positive_fft[idx]
            if freq > 1 and amplitude > 0.1:  # 过滤低频和小幅值
                main_freqs.append(f"{freq:.1f}Hz")
        
        print(f"\n{fault_name}:")
        print(f"  特征描述: {description}")
        print(f"  RMS值: {rms:.3f}")
        print(f"  峰值因子: {crest_factor:.2f}")
        print(f"  主要频率: {', '.join(main_freqs[:3])}")

def demo_maintenance_recommendations():
    """演示维护建议"""
    print("\n" + "=" * 60)
    print("维护建议演示")
    print("=" * 60)
    
    maintenance_guide = {
        '转子不平衡': {
            '预防措施': [
                '定期清洁叶片表面，防止积灰',
                '检查叶片完整性，及时修复损伤',
                '定期检查叶片安装螺栓紧固度'
            ],
            '处理方法': [
                '清洁叶片表面积灰',
                '进行动平衡校正',
                '更换损坏的叶片',
                '调整叶片配重'
            ],
            '监测重点': [
                '1X转频幅值变化趋势',
                '径向振动水平',
                '叶片外观检查'
            ]
        },
        '转子不对中': {
            '预防措施': [
                '定期检查基础稳定性',
                '监测轴承温度变化',
                '检查联轴器状态'
            ],
            '处理方法': [
                '检查并调整基础水平',
                '重新进行轴系对中',
                '更换磨损的轴承',
                '调整联轴器间隙'
            ],
            '监测重点': [
                '2X转频幅值变化',
                '轴向振动水平',
                '轴承温度监测'
            ]
        },
        '叶片故障': {
            '预防措施': [
                '定期进行叶片外观检查',
                '监测叶片表面状态',
                '防止叶片积冰'
            ],
            '处理方法': [
                '修复叶片表面裂纹',
                '更换严重损坏的叶片',
                '清除叶片表面积冰',
                '调整叶片角度'
            ],
            '监测重点': [
                '叶片通过频率幅值',
                '塔架振动水平',
                '叶片外观状态'
            ]
        }
    }
    
    for fault_type, guide in maintenance_guide.items():
        print(f"\n🔧 {fault_type} 维护指南：")
        print("-" * 30)
        
        print("预防措施：")
        for measure in guide['预防措施']:
            print(f"  • {measure}")
        
        print("处理方法：")
        for method in guide['处理方法']:
            print(f"  • {method}")
        
        print("监测重点：")
        for focus in guide['监测重点']:
            print(f"  • {focus}")

if __name__ == "__main__":
    print("🌪️ 风机故障诊断系统功能演示")
    print("支持故障类型：转子不平衡、转子不对中、叶片故障")
    
    # 运行演示
    demo_wind_turbine_fault_diagnosis()
    demo_fault_signal_analysis()
    demo_maintenance_recommendations()
    
    print("\n" + "=" * 60)
    print("✅ 演示完成！")
    print("系统已成功更新，去除了齿轮箱故障和发电机故障")
    print("现在专注于三种核心风机故障类型的诊断")
    print("=" * 60)
